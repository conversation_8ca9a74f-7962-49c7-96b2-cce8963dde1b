1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.moto_elmagic.app"
4    android:versionCode="1002"
5    android:versionName="1.2.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->G:\New folder\android\app\src\main\AndroidManifest.xml:5:5-67
11-->G:\New folder\android\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->G:\New folder\android\app\src\main\AndroidManifest.xml:6:5-79
12-->G:\New folder\android\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.RECORD_AUDIO" />
13-->G:\New folder\android\app\src\main\AndroidManifest.xml:7:5-70
13-->G:\New folder\android\app\src\main\AndroidManifest.xml:7:22-68
14    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
14-->G:\New folder\android\app\src\main\AndroidManifest.xml:8:5-79
14-->G:\New folder\android\app\src\main\AndroidManifest.xml:8:22-76
15    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
15-->G:\New folder\android\app\src\main\AndroidManifest.xml:9:5-81
15-->G:\New folder\android\app\src\main\AndroidManifest.xml:9:22-78
16    <uses-permission android:name="android.permission.CAMERA" />
16-->G:\New folder\android\app\src\main\AndroidManifest.xml:10:5-65
16-->G:\New folder\android\app\src\main\AndroidManifest.xml:10:22-62
17    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
17-->G:\New folder\android\app\src\main\AndroidManifest.xml:11:5-80
17-->G:\New folder\android\app\src\main\AndroidManifest.xml:11:22-77
18    <uses-permission
18-->G:\New folder\android\app\src\main\AndroidManifest.xml:12:5-146
19        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
19-->G:\New folder\android\app\src\main\AndroidManifest.xml:12:22-78
20        android:maxSdkVersion="32" />
20-->G:\New folder\android\app\src\main\AndroidManifest.xml:12:79-105
21    <uses-permission android:name="android.permission.VIBRATE" />
21-->G:\New folder\android\app\src\main\AndroidManifest.xml:13:5-66
21-->G:\New folder\android\app\src\main\AndroidManifest.xml:13:22-63
22    <!--
23         Required to query activities that can process text, see:
24         https://developer.android.com/training/package-visibility and
25         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
26
27         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
28    -->
29    <queries>
29-->G:\New folder\android\app\src\main\AndroidManifest.xml:69:5-74:15
30        <intent>
30-->G:\New folder\android\app\src\main\AndroidManifest.xml:70:9-73:18
31            <action android:name="android.intent.action.PROCESS_TEXT" />
31-->G:\New folder\android\app\src\main\AndroidManifest.xml:71:13-72
31-->G:\New folder\android\app\src\main\AndroidManifest.xml:71:21-70
32
33            <data android:mimeType="text/plain" />
33-->G:\New folder\android\app\src\main\AndroidManifest.xml:53:17-55:53
33-->G:\New folder\android\app\src\main\AndroidManifest.xml:72:19-48
34        </intent>
35        <intent>
35-->[:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-10.1.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:18
36            <action android:name="android.intent.action.GET_CONTENT" />
36-->[:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-10.1.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-72
36-->[:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-10.1.9\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:21-69
37
38            <data android:mimeType="*/*" />
38-->G:\New folder\android\app\src\main\AndroidManifest.xml:53:17-55:53
38-->G:\New folder\android\app\src\main\AndroidManifest.xml:72:19-48
39        </intent>
40    </queries>
41
42    <uses-feature android:name="android.hardware.camera.any" />
42-->[:camera_android_camerax] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\camera_android_camerax-0.6.15+2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-64
42-->[:camera_android_camerax] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\camera_android_camerax-0.6.15+2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:19-61
43
44    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
44-->[:flutter_local_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-19.2.1\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-77
44-->[:flutter_local_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-19.2.1\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-74
45
46    <permission
46-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
47        android:name="com.moto_elmagic.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
47-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
48        android:protectionLevel="signature" />
48-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
49
50    <uses-permission android:name="com.moto_elmagic.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
50-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
50-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
51
52    <application
52-->G:\New folder\android\app\src\main\AndroidManifest.xml:14:6-63:19
53        android:allowBackup="true"
53-->G:\New folder\android\app\src\main\AndroidManifest.xml:17:9-35
54        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
54-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
55        android:extractNativeLibs="false"
55-->G:\New folder\android\app\src\main\AndroidManifest.xml:22:9-42
56        android:hardwareAccelerated="true"
56-->G:\New folder\android\app\src\main\AndroidManifest.xml:18:9-43
57        android:icon="@mipmap/ic_launcher"
57-->G:\New folder\android\app\src\main\AndroidManifest.xml:16:9-43
58        android:label="الماجيك لقطع الغيار"
58-->G:\New folder\android\app\src\main\AndroidManifest.xml:15:9-44
59        android:largeHeap="true"
59-->G:\New folder\android\app\src\main\AndroidManifest.xml:19:9-33
60        android:requestLegacyExternalStorage="true"
60-->G:\New folder\android\app\src\main\AndroidManifest.xml:21:9-52
61        android:usesCleartextTraffic="true" >
61-->G:\New folder\android\app\src\main\AndroidManifest.xml:20:9-44
62        <activity
62-->G:\New folder\android\app\src\main\AndroidManifest.xml:23:9-57:20
63            android:name="com.moto_elmagic.app.MainActivity"
63-->G:\New folder\android\app\src\main\AndroidManifest.xml:24:13-41
64            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
64-->G:\New folder\android\app\src\main\AndroidManifest.xml:29:13-163
65            android:directBootAware="true"
65-->G:\New folder\android\app\src\main\AndroidManifest.xml:35:13-43
66            android:exported="true"
66-->G:\New folder\android\app\src\main\AndroidManifest.xml:25:13-36
67            android:hardwareAccelerated="true"
67-->G:\New folder\android\app\src\main\AndroidManifest.xml:30:13-47
68            android:launchMode="singleTop"
68-->G:\New folder\android\app\src\main\AndroidManifest.xml:26:13-43
69            android:resizeableActivity="false"
69-->G:\New folder\android\app\src\main\AndroidManifest.xml:33:13-47
70            android:screenOrientation="portrait"
70-->G:\New folder\android\app\src\main\AndroidManifest.xml:32:13-49
71            android:supportsPictureInPicture="false"
71-->G:\New folder\android\app\src\main\AndroidManifest.xml:34:13-53
72            android:taskAffinity=""
72-->G:\New folder\android\app\src\main\AndroidManifest.xml:27:13-36
73            android:theme="@style/LaunchTheme"
73-->G:\New folder\android\app\src\main\AndroidManifest.xml:28:13-47
74            android:windowSoftInputMode="adjustResize" >
74-->G:\New folder\android\app\src\main\AndroidManifest.xml:31:13-55
75
76            <!--
77                 Specifies an Android theme to apply to this Activity as soon as
78                 the Android process has started. This theme is visible to the user
79                 while the Flutter UI initializes. After that, this theme continues
80                 to determine the Window background behind the Flutter UI.
81            -->
82            <meta-data
82-->G:\New folder\android\app\src\main\AndroidManifest.xml:40:13-43:17
83                android:name="io.flutter.embedding.android.NormalTheme"
83-->G:\New folder\android\app\src\main\AndroidManifest.xml:41:15-70
84                android:resource="@style/NormalTheme" />
84-->G:\New folder\android\app\src\main\AndroidManifest.xml:42:15-52
85
86            <intent-filter>
86-->G:\New folder\android\app\src\main\AndroidManifest.xml:44:13-47:29
87                <action android:name="android.intent.action.MAIN" />
87-->G:\New folder\android\app\src\main\AndroidManifest.xml:45:17-68
87-->G:\New folder\android\app\src\main\AndroidManifest.xml:45:25-66
88
89                <category android:name="android.intent.category.LAUNCHER" />
89-->G:\New folder\android\app\src\main\AndroidManifest.xml:46:17-76
89-->G:\New folder\android\app\src\main\AndroidManifest.xml:46:27-74
90            </intent-filter>
91            <!-- إضافة إعدادات إعادة التوجيه لتسجيل الدخول بجوجل -->
92            <intent-filter>
92-->G:\New folder\android\app\src\main\AndroidManifest.xml:49:13-56:29
93                <action android:name="android.intent.action.VIEW" />
93-->G:\New folder\android\app\src\main\AndroidManifest.xml:50:17-69
93-->G:\New folder\android\app\src\main\AndroidManifest.xml:50:25-66
94
95                <category android:name="android.intent.category.DEFAULT" />
95-->G:\New folder\android\app\src\main\AndroidManifest.xml:51:17-76
95-->G:\New folder\android\app\src\main\AndroidManifest.xml:51:27-73
96                <category android:name="android.intent.category.BROWSABLE" />
96-->G:\New folder\android\app\src\main\AndroidManifest.xml:52:17-78
96-->G:\New folder\android\app\src\main\AndroidManifest.xml:52:27-75
97
98                <data
98-->G:\New folder\android\app\src\main\AndroidManifest.xml:53:17-55:53
99                    android:host="login-callback"
99-->G:\New folder\android\app\src\main\AndroidManifest.xml:55:21-50
100                    android:scheme="io.supabase.motorcycleparts" />
100-->G:\New folder\android\app\src\main\AndroidManifest.xml:54:21-65
101            </intent-filter>
102        </activity>
103        <!--
104             Don't delete the meta-data below.
105             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
106        -->
107        <meta-data
107-->G:\New folder\android\app\src\main\AndroidManifest.xml:60:9-62:33
108            android:name="flutterEmbedding"
108-->G:\New folder\android\app\src\main\AndroidManifest.xml:61:13-44
109            android:value="2" />
109-->G:\New folder\android\app\src\main\AndroidManifest.xml:62:13-30
110
111        <service
111-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:24:9-33:19
112            android:name="androidx.camera.core.impl.MetadataHolderService"
112-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:25:13-75
113            android:enabled="false"
113-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:26:13-36
114            android:exported="false" >
114-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:27:13-37
115            <meta-data
115-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
116                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
116-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
117                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
117-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
118        </service>
119        <service
119-->[:geolocator_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\geolocator_android-5.0.1+1\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:56
120            android:name="com.baseflow.geolocator.GeolocatorLocationService"
120-->[:geolocator_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\geolocator_android-5.0.1+1\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-77
121            android:enabled="true"
121-->[:geolocator_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\geolocator_android-5.0.1+1\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-35
122            android:exported="false"
122-->[:geolocator_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\geolocator_android-5.0.1+1\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-37
123            android:foregroundServiceType="location" />
123-->[:geolocator_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\geolocator_android-5.0.1+1\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-53
124
125        <provider
125-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-19:20
126            android:name="com.crazecoder.openfile.FileProvider"
126-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-64
127            android:authorities="com.moto_elmagic.app.fileProvider.com.crazecoder.openfile"
127-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-88
128            android:exported="false"
128-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-37
129            android:grantUriPermissions="true"
129-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-47
130            android:requestLegacyExternalStorage="true" >
130-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-56
131            <meta-data
131-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-18:53
132                android:name="android.support.FILE_PROVIDER_PATHS"
132-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:17-67
133                android:resource="@xml/filepaths" />
133-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:17-50
134        </provider>
135        <provider
135-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-17:20
136            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
136-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-82
137            android:authorities="com.moto_elmagic.app.flutter.image_provider"
137-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-74
138            android:exported="false"
138-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-37
139            android:grantUriPermissions="true" >
139-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-47
140            <meta-data
140-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-18:53
141                android:name="android.support.FILE_PROVIDER_PATHS"
141-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:17-67
142                android:resource="@xml/flutter_image_picker_file_paths" />
142-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:17-50
143        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
144        <service
144-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-31:19
145            android:name="com.google.android.gms.metadata.ModuleDependencies"
145-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-78
146            android:enabled="false"
146-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-36
147            android:exported="false" >
147-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-37
148            <intent-filter>
148-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-26:29
149                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
149-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-94
149-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-91
150            </intent-filter>
151
152            <meta-data
152-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-30:36
153                android:name="photopicker_activity:0:required"
153-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-63
154                android:value="" />
154-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-33
155        </service>
156
157        <provider
157-->[:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-16:20
158            android:name="net.nfet.flutter.printing.PrintFileProvider"
158-->[:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-71
159            android:authorities="com.moto_elmagic.app.flutter.printing"
159-->[:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-68
160            android:exported="false"
160-->[:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-37
161            android:grantUriPermissions="true" >
161-->[:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-47
162            <meta-data
162-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-18:53
163                android:name="android.support.FILE_PROVIDER_PATHS"
163-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:17-67
164                android:resource="@xml/flutter_printing_file_paths" />
164-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:17-50
165        </provider>
166        <!--
167           Declares a provider which allows us to store files to share in
168           '.../caches/share_plus' and grant the receiving action access
169        -->
170        <provider
170-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:9-21:20
171            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
171-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-77
172            android:authorities="com.moto_elmagic.app.flutter.share_provider"
172-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-74
173            android:exported="false"
173-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-37
174            android:grantUriPermissions="true" >
174-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:13-47
175            <meta-data
175-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-18:53
176                android:name="android.support.FILE_PROVIDER_PATHS"
176-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:17-67
177                android:resource="@xml/flutter_share_file_paths" />
177-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:17-50
178        </provider>
179        <!--
180           This manifest declared broadcast receiver allows us to use an explicit
181           Intent when creating a PendingItent to be informed of the user's choice
182        -->
183        <receiver
183-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:9-32:20
184            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
184-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:13-82
185            android:exported="false" >
185-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-37
186            <intent-filter>
186-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-31:29
187                <action android:name="EXTRA_CHOSEN_COMPONENT" />
187-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-65
187-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:25-62
188            </intent-filter>
189        </receiver>
190
191        <activity
191-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-11:74
192            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
192-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-74
193            android:exported="false"
193-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-37
194            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
194-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-71
195
196        <provider
196-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
197            android:name="androidx.startup.InitializationProvider"
197-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
198            android:authorities="com.moto_elmagic.app.androidx-startup"
198-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
199            android:exported="false" >
199-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
200            <meta-data
200-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
201                android:name="androidx.emoji2.text.EmojiCompatInitializer"
201-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
202                android:value="androidx.startup" />
202-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
203            <meta-data
203-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc3934e7b5992873779b6c0d978229f8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
204                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
204-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc3934e7b5992873779b6c0d978229f8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
205                android:value="androidx.startup" />
205-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc3934e7b5992873779b6c0d978229f8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
206            <meta-data
206-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
207                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
207-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
208                android:value="androidx.startup" />
208-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
209        </provider>
210
211        <activity
211-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
212            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
212-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
213            android:excludeFromRecents="true"
213-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
214            android:exported="false"
214-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
215            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
215-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
216        <!--
217            Service handling Google Sign-In user revocation. For apps that do not integrate with
218            Google Sign-In, this service will never be started.
219        -->
220        <service
220-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
221            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
221-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
222            android:exported="true"
222-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
223            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
223-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
224            android:visibleToInstantApps="true" />
224-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
225
226        <activity
226-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b93785a04fcd49acabbaa600426866db\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
227            android:name="com.google.android.gms.common.api.GoogleApiActivity"
227-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b93785a04fcd49acabbaa600426866db\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
228            android:exported="false"
228-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b93785a04fcd49acabbaa600426866db\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
229            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
229-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b93785a04fcd49acabbaa600426866db\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
230
231        <meta-data
231-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48bb13e58e49cdf1e6377472107b28a8\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
232            android:name="com.google.android.gms.version"
232-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48bb13e58e49cdf1e6377472107b28a8\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
233            android:value="@integer/google_play_services_version" />
233-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48bb13e58e49cdf1e6377472107b28a8\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
234
235        <uses-library
235-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
236            android:name="androidx.window.extensions"
236-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
237            android:required="false" />
237-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
238        <uses-library
238-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
239            android:name="androidx.window.sidecar"
239-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
240            android:required="false" />
240-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
241
242        <receiver
242-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
243            android:name="androidx.profileinstaller.ProfileInstallReceiver"
243-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
244            android:directBootAware="false"
244-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
245            android:enabled="true"
245-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
246            android:exported="true"
246-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
247            android:permission="android.permission.DUMP" >
247-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
248            <intent-filter>
248-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
249                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
249-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
249-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
250            </intent-filter>
251            <intent-filter>
251-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
252                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
252-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
252-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
253            </intent-filter>
254            <intent-filter>
254-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
255                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
256            </intent-filter>
257            <intent-filter>
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
258                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
259            </intent-filter>
260        </receiver>
261    </application>
262
263</manifest>
