-- Merging decision tree log ---
manifest
ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:1:1-76:12
MERGED from G:\New folder\android\app\src\main\AndroidManifest.xml:1:1-76:12
INJECTED from G:\New folder\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from G:\New folder\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from G:\New folder\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:camera_android_camerax] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\camera_android_camerax-0.6.15+2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-10.1.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:shared_preferences_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\shared_preferences_android-2.4.10\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:speech_to_text] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\speech_to_text-7.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.play:core-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d7e52ffc7b569e696a7665ae044d0e4\transformed\jetified-core-ktx-1.8.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\062308797b9e4d6b88ecc1984bddba1f\transformed\jetified-camera-video-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5271d7f9afefeb924f43e72ad2df04d0\transformed\jetified-camera-lifecycle-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ead12b878b4ca16f11e58c623d32431\transformed\jetified-camera-core-1.4.1\AndroidManifest.xml:17:1-36:12
MERGED from [:geolocator_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\geolocator_android-5.0.1+1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc40bc5f6e40c88241fb805931d29c4c\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-22:12
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10d17790f6d6a95ec46bd68f81a8cd4a\transformed\material-1.9.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12a085f4af97d8500c9432a48145ea1b\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\159a470d7aae973f3fe188f99bb68c27\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d7310a1c2022cc66dcbf0194328e17e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37c9757273d42d5a9fb8fafd595f355e\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [:app_links] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\app_links-6.4.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:connectivity_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-6.1.4\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:device_info_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\device_info_plus-11.4.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_local_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-19.2.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-34:12
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_plugin_android_lifecycle-2.0.28\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:google_sign_in_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\google_sign_in_android-6.2.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:integration_test] C:\flutter\packages\integration_test\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:1-12:12
MERGED from [:package_info_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\package_info_plus-8.3.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:path_provider_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\path_provider_android-2.2.17\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-19:12
MERGED from [:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-35:12
MERGED from [:sqflite_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\sqflite_android-2.4.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-14:12
MERGED from [:vibration] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\vibration-3.1.3\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8597dd861fdb19aeb54265ac9d517aa0\transformed\media-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:17:1-40:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f17c1b104cf3d8824413c2c5c47d7e0a\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\04f0f0676f3643357eaa49f81d75cd2e\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b29cdb341ee6ce222a593dfe9e4e8a4\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03db382edb63c57d8de629e444088e69\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b93785a04fcd49acabbaa600426866db\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0dd44f6bde9aaae99011f8ea15baea7\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48bb13e58e49cdf1e6377472107b28a8\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da563446475d3ff73b8066be02648858\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3a986fc94dc98dab92da85e4b25558\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d9662a0e7c297833ce8fabe4baa8c18\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4945d9b5f034fedd21e910069a69270\transformed\jetified-activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\df22b875ff6a90ec3ebad0ef728bb68b\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4178d61b28093b5fd88b12cbd43b0b07\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8862ab24c9c871dc4870b49feae83655\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9073bf44004f6680be689a11ee51aed1\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\690fa2dc9a925dfa50efa8fa92d6ecf6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f09a68f32073750392738f19795bebb\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\760bf388237b0fe561efd518543baa2a\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\371fb012b93199196053d38abf571a3b\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2be31f5f98c024d17eda5160252de46\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7264ae1be065c300b7a81eb08f37927\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b19d15f8d9a13fc8029cba36d8cb5768\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf4bcb5d0d3efd13db4a1c454d9ea19e\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f536ab661db8c110fa8f555d4045bd\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f7643b9b1bad717c69a69c916da5a3d\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67a2c0bce764893ed5ee62babcf95e47\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87c7c578e067ffb33addf00ca552366a\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0cc9e041ee9796f451c27b0b143dd9fb\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44af30a5fc77cd3f60208e9fc9c16633\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc3934e7b5992873779b6c0d978229f8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adaffbc5a620e348809bca4a335f9443\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f2cf84bbd1469b68383d45d56561f84\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\551c016364080aaa90d415bcfba95d8a\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac252eaaa763e553c1d6668f744608ba\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7204b720b0ad484ba3dbf0dd1c5b6ae\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d52ea09101bb351c12b3e49b2217222\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0862ad865ccaefe4335f806706ea808c\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acc2838b1a488e47887846a836b594b6\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27c4dfa5d9334dc3020f9c1a61e421ba\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd04105ba440b87656fbec3770c0eb86\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c685b63e685b2dbf34c656aff361de0a\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7da7405cd30359ca2f4adf8f558bb582\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bf4e494cae922786c1836daab839078\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffafe5f631203f4924364ba072ee19e0\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c54165b066b4ebdf8a3c03617e1f28b\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb3550aaefe6264f927b55ae2c124f04\transformed\jetified-tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42a647dddfd4d90dd7eff806611d6372\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test:rules:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3527b230438e0d79edd824d69157df7c\transformed\rules-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test.espresso:espresso-core:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b70b3c93bbe42305fbcef173e08ddf5\transformed\espresso-core-3.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test:runner:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\717a754dc78736c0b62c80b54a3123e8\transformed\runner-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69e5ac8e407d7c551f1f5ec5856a45aa\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2d01fc18af3d5a70987d316fee91f64a\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c33255f411e345b8c9153cdc8b772a9\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1d97c503b3a4bd9cc3718f3fd84010a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.test:monitor:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1862a52275eebc1797eb2ed6889fd74d\transformed\monitor-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2191f642bb5d35c54ec69e277330946c\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c5e10639dec331e62d9fbf86705cb71\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e4bb0fcc1e64f8f5b3851cfa642d690\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a849f704a19700db11ee500f47aae641\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\214b8070ae4c800f920040867b251ddc\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\157c9398923e3f7ff16a8d0bd43a6556\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:2:1-46:12
MERGED from [org.microg:safe-parcel:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d19677b8561c460fcccfdefdde5a8cd4\transformed\jetified-safe-parcel-1.7.0\AndroidManifest.xml:6:1-14:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1841dfdc9324175fac5338109cf574de\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.test.espresso:espresso-idling-resource:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44455119a54d4c7c6b29c58314f5ed5a\transformed\espresso-idling-resource-3.2.0\AndroidManifest.xml:17:1-24:12
	package
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:3:5-35
		INJECTED from G:\New folder\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from G:\New folder\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:2:5-51
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from G:\New folder\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from G:\New folder\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from G:\New folder\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from [:google_sign_in_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\google_sign_in_android-6.2.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:google_sign_in_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\google_sign_in_android-6.2.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
	android:name
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:6:5-79
MERGED from [:connectivity_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-6.1.4\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:connectivity_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-6.1.4\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
	android:name
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.RECORD_AUDIO
ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:7:5-70
MERGED from [:camera_android_camerax] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\camera_android_camerax-0.6.15+2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-71
MERGED from [:camera_android_camerax] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\camera_android_camerax-0.6.15+2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-71
	android:name
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:7:22-68
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:8:5-79
	android:name
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:8:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:9:5-81
	android:name
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:9:22-78
uses-permission#android.permission.CAMERA
ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:10:5-65
MERGED from [:camera_android_camerax] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\camera_android_camerax-0.6.15+2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-65
MERGED from [:camera_android_camerax] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\camera_android_camerax-0.6.15+2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-65
	android:name
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:10:22-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:11:5-80
	android:name
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:11:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:12:5-146
MERGED from [:camera_android_camerax] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\camera_android_camerax-0.6.15+2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-13:38
MERGED from [:camera_android_camerax] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\camera_android_camerax-0.6.15+2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-13:38
	android:maxSdkVersion
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:12:79-105
		REJECTED from [:camera_android_camerax] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\camera_android_camerax-0.6.15+2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-35
	tools:replace
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:12:106-143
	android:name
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:12:22-78
uses-permission#android.permission.VIBRATE
ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:13:5-66
MERGED from [:flutter_local_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-19.2.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
MERGED from [:flutter_local_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-19.2.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
MERGED from [:vibration] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\vibration-3.1.3\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
MERGED from [:vibration] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\vibration-3.1.3\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
	android:name
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:13:22-63
application
ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:14:6-63:19
MERGED from [com.google.android.play:core-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d7e52ffc7b569e696a7665ae044d0e4\transformed\jetified-core-ktx-1.8.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.play:core-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d7e52ffc7b569e696a7665ae044d0e4\transformed\jetified-core-ktx-1.8.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ead12b878b4ca16f11e58c623d32431\transformed\jetified-camera-core-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ead12b878b4ca16f11e58c623d32431\transformed\jetified-camera-core-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [:geolocator_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\geolocator_android-5.0.1+1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:geolocator_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\geolocator_android-5.0.1+1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc40bc5f6e40c88241fb805931d29c4c\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc40bc5f6e40c88241fb805931d29c4c\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-20:19
MERGED from [:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-20:19
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10d17790f6d6a95ec46bd68f81a8cd4a\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10d17790f6d6a95ec46bd68f81a8cd4a\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d7310a1c2022cc66dcbf0194328e17e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d7310a1c2022cc66dcbf0194328e17e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-33:19
MERGED from [:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-33:19
MERGED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03db382edb63c57d8de629e444088e69\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03db382edb63c57d8de629e444088e69\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b93785a04fcd49acabbaa600426866db\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b93785a04fcd49acabbaa600426866db\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0dd44f6bde9aaae99011f8ea15baea7\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0dd44f6bde9aaae99011f8ea15baea7\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48bb13e58e49cdf1e6377472107b28a8\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48bb13e58e49cdf1e6377472107b28a8\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc3934e7b5992873779b6c0d978229f8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc3934e7b5992873779b6c0d978229f8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffafe5f631203f4924364ba072ee19e0\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffafe5f631203f4924364ba072ee19e0\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1d97c503b3a4bd9cc3718f3fd84010a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1d97c503b3a4bd9cc3718f3fd84010a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:15:5-44:19
MERGED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:15:5-44:19
	android:requestLegacyExternalStorage
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:21:9-52
	android:extractNativeLibs
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:22:9-42
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:label
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:15:9-44
	android:hardwareAccelerated
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:18:9-43
	android:largeHeap
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:19:9-33
	android:icon
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:16:9-43
	android:allowBackup
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:17:9-35
	android:usesCleartextTraffic
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:20:9-44
activity#com.moto_elmagic.app.MainActivity
ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:23:9-57:20
	android:screenOrientation
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:32:13-49
	android:launchMode
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:26:13-43
	android:hardwareAccelerated
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:30:13-47
	android:windowSoftInputMode
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:31:13-55
	android:exported
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:25:13-36
	android:supportsPictureInPicture
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:34:13-53
	android:resizeableActivity
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:33:13-47
	android:configChanges
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:29:13-163
	android:theme
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:28:13-47
	android:directBootAware
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:35:13-43
	android:taskAffinity
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:27:13-36
	android:name
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:24:13-41
meta-data#io.flutter.embedding.android.NormalTheme
ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:40:13-43:17
	android:resource
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:42:15-52
	android:name
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:41:15-70
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:44:13-47:29
action#android.intent.action.MAIN
ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:45:17-68
	android:name
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:45:25-66
category#android.intent.category.LAUNCHER
ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:46:17-76
	android:name
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:46:27-74
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:login-callback+data:scheme:io.supabase.motorcycleparts
ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:49:13-56:29
action#android.intent.action.VIEW
ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:50:17-69
	android:name
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:50:25-66
category#android.intent.category.DEFAULT
ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:51:17-76
	android:name
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:51:27-73
category#android.intent.category.BROWSABLE
ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:52:17-78
	android:name
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:52:27-75
data
ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:53:17-55:53
	android:host
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:55:21-50
	android:scheme
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:54:21-65
meta-data#flutterEmbedding
ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:60:9-62:33
	android:value
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:62:13-30
	android:name
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:61:13-44
queries
ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:69:5-74:15
MERGED from [:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-10.1.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:15
MERGED from [:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-10.1.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:70:9-73:18
action#android.intent.action.PROCESS_TEXT
ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:71:13-72
	android:name
		ADDED from G:\New folder\android\app\src\main\AndroidManifest.xml:71:21-70
uses-sdk
INJECTED from G:\New folder\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from G:\New folder\android\app\src\debug\AndroidManifest.xml
INJECTED from G:\New folder\android\app\src\debug\AndroidManifest.xml
MERGED from [:camera_android_camerax] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\camera_android_camerax-0.6.15+2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:camera_android_camerax] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\camera_android_camerax-0.6.15+2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-10.1.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-10.1.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\shared_preferences_android-2.4.10\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\shared_preferences_android-2.4.10\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:speech_to_text] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\speech_to_text-7.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:speech_to_text] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\speech_to_text-7.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:core-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d7e52ffc7b569e696a7665ae044d0e4\transformed\jetified-core-ktx-1.8.1\AndroidManifest.xml:5:5-43
MERGED from [com.google.android.play:core-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d7e52ffc7b569e696a7665ae044d0e4\transformed\jetified-core-ktx-1.8.1\AndroidManifest.xml:5:5-43
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\062308797b9e4d6b88ecc1984bddba1f\transformed\jetified-camera-video-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\062308797b9e4d6b88ecc1984bddba1f\transformed\jetified-camera-video-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5271d7f9afefeb924f43e72ad2df04d0\transformed\jetified-camera-lifecycle-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5271d7f9afefeb924f43e72ad2df04d0\transformed\jetified-camera-lifecycle-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ead12b878b4ca16f11e58c623d32431\transformed\jetified-camera-core-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ead12b878b4ca16f11e58c623d32431\transformed\jetified-camera-core-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [:geolocator_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\geolocator_android-5.0.1+1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:geolocator_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\geolocator_android-5.0.1+1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc40bc5f6e40c88241fb805931d29c4c\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc40bc5f6e40c88241fb805931d29c4c\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10d17790f6d6a95ec46bd68f81a8cd4a\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10d17790f6d6a95ec46bd68f81a8cd4a\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12a085f4af97d8500c9432a48145ea1b\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12a085f4af97d8500c9432a48145ea1b\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\159a470d7aae973f3fe188f99bb68c27\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\159a470d7aae973f3fe188f99bb68c27\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d7310a1c2022cc66dcbf0194328e17e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d7310a1c2022cc66dcbf0194328e17e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37c9757273d42d5a9fb8fafd595f355e\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37c9757273d42d5a9fb8fafd595f355e\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [:app_links] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\app_links-6.4.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:app_links] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\app_links-6.4.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-6.1.4\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-6.1.4\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:device_info_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\device_info_plus-11.4.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:device_info_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\device_info_plus-11.4.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-19.2.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-19.2.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_plugin_android_lifecycle-2.0.28\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_plugin_android_lifecycle-2.0.28\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\google_sign_in_android-6.2.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\google_sign_in_android-6.2.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:integration_test] C:\flutter\packages\integration_test\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-44
MERGED from [:integration_test] C:\flutter\packages\integration_test\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-44
MERGED from [:package_info_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\package_info_plus-8.3.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\package_info_plus-8.3.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\path_provider_android-2.2.17\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\path_provider_android-2.2.17\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\sqflite_android-2.4.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\sqflite_android-2.4.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:vibration] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\vibration-3.1.3\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:vibration] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\vibration-3.1.3\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8597dd861fdb19aeb54265ac9d517aa0\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8597dd861fdb19aeb54265ac9d517aa0\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f17c1b104cf3d8824413c2c5c47d7e0a\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f17c1b104cf3d8824413c2c5c47d7e0a\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\04f0f0676f3643357eaa49f81d75cd2e\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\04f0f0676f3643357eaa49f81d75cd2e\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b29cdb341ee6ce222a593dfe9e4e8a4\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b29cdb341ee6ce222a593dfe9e4e8a4\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03db382edb63c57d8de629e444088e69\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03db382edb63c57d8de629e444088e69\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b93785a04fcd49acabbaa600426866db\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b93785a04fcd49acabbaa600426866db\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0dd44f6bde9aaae99011f8ea15baea7\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0dd44f6bde9aaae99011f8ea15baea7\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48bb13e58e49cdf1e6377472107b28a8\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48bb13e58e49cdf1e6377472107b28a8\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da563446475d3ff73b8066be02648858\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da563446475d3ff73b8066be02648858\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3a986fc94dc98dab92da85e4b25558\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3a986fc94dc98dab92da85e4b25558\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d9662a0e7c297833ce8fabe4baa8c18\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d9662a0e7c297833ce8fabe4baa8c18\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4945d9b5f034fedd21e910069a69270\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4945d9b5f034fedd21e910069a69270\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\df22b875ff6a90ec3ebad0ef728bb68b\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\df22b875ff6a90ec3ebad0ef728bb68b\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4178d61b28093b5fd88b12cbd43b0b07\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4178d61b28093b5fd88b12cbd43b0b07\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8862ab24c9c871dc4870b49feae83655\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8862ab24c9c871dc4870b49feae83655\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9073bf44004f6680be689a11ee51aed1\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9073bf44004f6680be689a11ee51aed1\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\690fa2dc9a925dfa50efa8fa92d6ecf6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\690fa2dc9a925dfa50efa8fa92d6ecf6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f09a68f32073750392738f19795bebb\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f09a68f32073750392738f19795bebb\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\760bf388237b0fe561efd518543baa2a\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\760bf388237b0fe561efd518543baa2a\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\371fb012b93199196053d38abf571a3b\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\371fb012b93199196053d38abf571a3b\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2be31f5f98c024d17eda5160252de46\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2be31f5f98c024d17eda5160252de46\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7264ae1be065c300b7a81eb08f37927\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7264ae1be065c300b7a81eb08f37927\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b19d15f8d9a13fc8029cba36d8cb5768\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b19d15f8d9a13fc8029cba36d8cb5768\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf4bcb5d0d3efd13db4a1c454d9ea19e\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf4bcb5d0d3efd13db4a1c454d9ea19e\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f536ab661db8c110fa8f555d4045bd\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8f536ab661db8c110fa8f555d4045bd\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f7643b9b1bad717c69a69c916da5a3d\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f7643b9b1bad717c69a69c916da5a3d\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67a2c0bce764893ed5ee62babcf95e47\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67a2c0bce764893ed5ee62babcf95e47\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87c7c578e067ffb33addf00ca552366a\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87c7c578e067ffb33addf00ca552366a\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0cc9e041ee9796f451c27b0b143dd9fb\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0cc9e041ee9796f451c27b0b143dd9fb\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44af30a5fc77cd3f60208e9fc9c16633\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44af30a5fc77cd3f60208e9fc9c16633\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc3934e7b5992873779b6c0d978229f8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc3934e7b5992873779b6c0d978229f8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adaffbc5a620e348809bca4a335f9443\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adaffbc5a620e348809bca4a335f9443\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f2cf84bbd1469b68383d45d56561f84\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f2cf84bbd1469b68383d45d56561f84\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\551c016364080aaa90d415bcfba95d8a\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\551c016364080aaa90d415bcfba95d8a\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac252eaaa763e553c1d6668f744608ba\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac252eaaa763e553c1d6668f744608ba\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7204b720b0ad484ba3dbf0dd1c5b6ae\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7204b720b0ad484ba3dbf0dd1c5b6ae\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d52ea09101bb351c12b3e49b2217222\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d52ea09101bb351c12b3e49b2217222\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0862ad865ccaefe4335f806706ea808c\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0862ad865ccaefe4335f806706ea808c\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acc2838b1a488e47887846a836b594b6\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acc2838b1a488e47887846a836b594b6\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27c4dfa5d9334dc3020f9c1a61e421ba\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27c4dfa5d9334dc3020f9c1a61e421ba\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd04105ba440b87656fbec3770c0eb86\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd04105ba440b87656fbec3770c0eb86\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c685b63e685b2dbf34c656aff361de0a\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c685b63e685b2dbf34c656aff361de0a\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7da7405cd30359ca2f4adf8f558bb582\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7da7405cd30359ca2f4adf8f558bb582\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bf4e494cae922786c1836daab839078\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bf4e494cae922786c1836daab839078\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffafe5f631203f4924364ba072ee19e0\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffafe5f631203f4924364ba072ee19e0\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c54165b066b4ebdf8a3c03617e1f28b\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c54165b066b4ebdf8a3c03617e1f28b\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb3550aaefe6264f927b55ae2c124f04\transformed\jetified-tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb3550aaefe6264f927b55ae2c124f04\transformed\jetified-tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42a647dddfd4d90dd7eff806611d6372\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42a647dddfd4d90dd7eff806611d6372\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:rules:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3527b230438e0d79edd824d69157df7c\transformed\rules-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:rules:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3527b230438e0d79edd824d69157df7c\transformed\rules-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.espresso:espresso-core:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b70b3c93bbe42305fbcef173e08ddf5\transformed\espresso-core-3.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.espresso:espresso-core:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b70b3c93bbe42305fbcef173e08ddf5\transformed\espresso-core-3.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:runner:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\717a754dc78736c0b62c80b54a3123e8\transformed\runner-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:runner:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\717a754dc78736c0b62c80b54a3123e8\transformed\runner-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69e5ac8e407d7c551f1f5ec5856a45aa\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69e5ac8e407d7c551f1f5ec5856a45aa\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2d01fc18af3d5a70987d316fee91f64a\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2d01fc18af3d5a70987d316fee91f64a\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c33255f411e345b8c9153cdc8b772a9\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c33255f411e345b8c9153cdc8b772a9\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1d97c503b3a4bd9cc3718f3fd84010a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1d97c503b3a4bd9cc3718f3fd84010a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:monitor:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1862a52275eebc1797eb2ed6889fd74d\transformed\monitor-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:monitor:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1862a52275eebc1797eb2ed6889fd74d\transformed\monitor-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2191f642bb5d35c54ec69e277330946c\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2191f642bb5d35c54ec69e277330946c\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c5e10639dec331e62d9fbf86705cb71\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c5e10639dec331e62d9fbf86705cb71\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e4bb0fcc1e64f8f5b3851cfa642d690\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e4bb0fcc1e64f8f5b3851cfa642d690\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a849f704a19700db11ee500f47aae641\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a849f704a19700db11ee500f47aae641\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\214b8070ae4c800f920040867b251ddc\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\214b8070ae4c800f920040867b251ddc\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\157c9398923e3f7ff16a8d0bd43a6556\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\157c9398923e3f7ff16a8d0bd43a6556\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:7:5-9:41
MERGED from [org.microg:safe-parcel:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d19677b8561c460fcccfdefdde5a8cd4\transformed\jetified-safe-parcel-1.7.0\AndroidManifest.xml:10:5-12:41
MERGED from [org.microg:safe-parcel:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d19677b8561c460fcccfdefdde5a8cd4\transformed\jetified-safe-parcel-1.7.0\AndroidManifest.xml:10:5-12:41
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1841dfdc9324175fac5338109cf574de\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1841dfdc9324175fac5338109cf574de\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [androidx.test.espresso:espresso-idling-resource:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44455119a54d4c7c6b29c58314f5ed5a\transformed\espresso-idling-resource-3.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.espresso:espresso-idling-resource:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44455119a54d4c7c6b29c58314f5ed5a\transformed\espresso-idling-resource-3.2.0\AndroidManifest.xml:20:5-22:41
	android:targetSdkVersion
		INJECTED from G:\New folder\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from G:\New folder\android\app\src\debug\AndroidManifest.xml
uses-feature#android.hardware.camera.any
ADDED from [:camera_android_camerax] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\camera_android_camerax-0.6.15+2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-64
	android:name
		ADDED from [:camera_android_camerax] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\camera_android_camerax-0.6.15+2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:19-61
intent#action:name:android.intent.action.GET_CONTENT+data:mimeType:*/*
ADDED from [:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-10.1.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
action#android.intent.action.GET_CONTENT
ADDED from [:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-10.1.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
	android:name
		ADDED from [:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-10.1.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ead12b878b4ca16f11e58c623d32431\transformed\jetified-camera-core-1.4.1\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ead12b878b4ca16f11e58c623d32431\transformed\jetified-camera-core-1.4.1\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
service#com.baseflow.geolocator.GeolocatorLocationService
ADDED from [:geolocator_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\geolocator_android-5.0.1+1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
	android:enabled
		ADDED from [:geolocator_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\geolocator_android-5.0.1+1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
	android:exported
		ADDED from [:geolocator_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\geolocator_android-5.0.1+1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:foregroundServiceType
		ADDED from [:geolocator_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\geolocator_android-5.0.1+1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
	android:name
		ADDED from [:geolocator_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\geolocator_android-5.0.1+1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
provider#com.crazecoder.openfile.FileProvider
ADDED from [:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-19:20
	android:requestLegacyExternalStorage
		ADDED from [:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-56
	android:grantUriPermissions
		ADDED from [:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-88
	android:exported
		ADDED from [:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
	tools:replace
		ADDED from [:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-48
	android:name
		ADDED from [:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-18:53
	android:resource
		ADDED from [:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:17-50
	android:name
		ADDED from [:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:17-67
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [:flutter_local_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-19.2.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
	android:name
		ADDED from [:flutter_local_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-19.2.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
provider#net.nfet.flutter.printing.PrintFileProvider
ADDED from [:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
	android:grantUriPermissions
		ADDED from [:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
	android:authorities
		ADDED from [:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-68
	android:exported
		ADDED from [:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-71
provider#dev.fluttercommunity.plus.share.ShareFileProvider
ADDED from [:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:20
	android:grantUriPermissions
		ADDED from [:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-47
	android:authorities
		ADDED from [:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-74
	android:exported
		ADDED from [:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
	android:name
		ADDED from [:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-77
receiver#dev.fluttercommunity.plus.share.SharePlusPendingIntent
ADDED from [:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-32:20
	android:exported
		ADDED from [:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-82
intent-filter#action:name:EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-31:29
action#EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-65
	android:name
		ADDED from [:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:25-62
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b93785a04fcd49acabbaa600426866db\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b93785a04fcd49acabbaa600426866db\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b93785a04fcd49acabbaa600426866db\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b93785a04fcd49acabbaa600426866db\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48bb13e58e49cdf1e6377472107b28a8\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48bb13e58e49cdf1e6377472107b28a8\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48bb13e58e49cdf1e6377472107b28a8\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc3934e7b5992873779b6c0d978229f8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc3934e7b5992873779b6c0d978229f8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffafe5f631203f4924364ba072ee19e0\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffafe5f631203f4924364ba072ee19e0\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc3934e7b5992873779b6c0d978229f8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc3934e7b5992873779b6c0d978229f8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc3934e7b5992873779b6c0d978229f8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.moto_elmagic.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.moto_elmagic.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:13:5-77
	android:name
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:13:22-74
activity#com.google.android.play.core.missingsplits.PlayCoreMissingSplitsActivity
ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:18:9-24:45
	android:process
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:23:13-64
	android:enabled
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:20:13-36
	android:stateNotNeeded
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:24:13-42
	android:launchMode
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:22:13-48
	android:exported
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:19:13-100
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:25:9-29:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:28:13-42
	android:exported
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:27:13-37
	android:theme
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:29:13-62
	android:name
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:26:13-93
service#com.google.android.play.core.assetpacks.AssetPackExtractionService
ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:32:9-39:19
	android:enabled
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:34:13-36
	android:exported
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:35:13-36
	android:name
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:33:13-94
meta-data#com.google.android.play.core.assetpacks.versionCode
ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:36:13-38:41
	android:value
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:38:17-38
	android:name
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:37:17-83
service#com.google.android.play.core.assetpacks.ExtractionForegroundService
ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:40:9-43:40
	android:enabled
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:42:13-36
	android:exported
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:43:13-37
	android:name
		ADDED from [com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:41:13-95
