"-Xallow-no-source-files" "-classpath" "G:\\New folder\\android\\app\\build\\intermediates\\compile_and_runtime_not_namespaced_r_class_jar\\release\\processReleaseResources\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b73df393beed04f893e8ad629d2c3aa\\transformed\\jetified-libs.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.15+2\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_picker-10.1.9\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\speech_to_text-7.0.0\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3e74a9822001bfcf5830255e4b5e764\\transformed\\material-1.9.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\efecb7d4fcec9153116c703529a7ca43\\transformed\\constraintlayout-2.0.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3c54cfc3406edfa4960e64f3769c466c\\transformed\\appcompat-1.6.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\45d4953dad2c569329f9e46b0dd77ce2\\transformed\\jetified-viewpager2-1.0.0-api.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\app_links-6.4.0\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\connectivity_plus-6.1.4\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus-11.4.0\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_local_notifications-19.2.1\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_plugin_android_lifecycle-2.0.28\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator_android-5.0.1+1\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_sign_in_android-6.2.1\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_android-0.8.12+23\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\flutter\\packages\\integration_test\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\open_file_android-1.0.6\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_info_plus-8.3.0\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_android-2.2.17\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\printing-5.14.2\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\share_plus-11.0.0\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_android-2.4.1\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_android-6.3.16\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vibration-3.1.3\\android\\build\\intermediates\\compile_library_classes_jar\\release\\bundleLibCompileToJarRelease\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97829635146f86d22f46d65949b0d935\\transformed\\jetified-flutter_embedding_release-1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bfd75ba55ab5a00bcb902fda024a4748\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\65a18adffe427ba08fd3753e1025f637\\transformed\\jetified-activity-1.9.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\65a182392f3d8caf4892044182317761\\transformed\\dynamicanimation-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8a89f653091c1aac604d082d3c702d5e\\transformed\\legacy-support-core-utils-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e8fb0a3525adce1639526e41b36c9e26\\transformed\\loader-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\210e7b881797ccca7dfab99d20f26903\\transformed\\jetified-appcompat-resources-1.6.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e6d5f349968848182a883ac2bc1784f3\\transformed\\drawerlayout-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\add9796a01a76470223a19d945d3f10c\\transformed\\coordinatorlayout-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\909a640946d913de8f585a3c7ba4ea06\\transformed\\recyclerview-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9ccbdc8838795f5388a8f4a4dd748e77\\transformed\\transition-1.4.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dd1fd1b4c530d0c043d7114f984d03f6\\transformed\\vectordrawable-animated-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\33afb4ae2aea676c1269bba8c0347ff9\\transformed\\vectordrawable-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\75382f35708d64fd823c9e2746573eee\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5bb978679b4abb430fe095cc3fc2f6d9\\transformed\\customview-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d0abc06292055f5f473f90666a6c91e6\\transformed\\core-1.16.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\10e6b411bb0b707be0fd7aeaed92521a\\transformed\\jetified-lifecycle-common-jvm-2.8.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\29205d37aac4a1bf753c3b9373f93cb9\\transformed\\lifecycle-viewmodel-2.8.7-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97aec6b005073c43f0bf7bafbeb93b10\\transformed\\jetified-lifecycle-viewmodel-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7b9fa5dd2291f93a8210b5898166c7d3\\transformed\\jetified-lifecycle-process-2.8.7-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.8.7\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.8.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca9f5b90eff9f6853b5081a683406155\\transformed\\jetified-lifecycle-runtime-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4877cfb3748de0590f121e42a3bf2352\\transformed\\lifecycle-livedata-core-2.8.7-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cd5ec13c1717c1209a25eb995d43b168\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.8.7-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5124a85f307552223b8ce471a5470588\\transformed\\jetified-core-ktx-1.16.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\635d0f187e9ebb5d4951326d0f905015\\transformed\\jetified-kotlin-stdlib-jdk7-1.8.22.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9ba5b20df595e6da6bd46a5590aeb004\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\84c7b44c0fc96f3ecc7b1dab09f7bc17\\transformed\\jetified-annotation-experimental-1.4.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fd7130ee6906cbc2ebc64d647a0e22fe\\transformed\\cursoradapter-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\56672b344212f73d0ad1ca73515ca6ab\\transformed\\cardview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a019480aed2874a86d4e3ebe0baf8609\\transformed\\rules-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8e53bc25ae0b9eb86fd16ff42c7cc745\\transformed\\espresso-core-3.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6d3913d89237de4ff98da24f8175a609\\transformed\\runner-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aecb74e9047658ee015b167174ffad52\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\eb69f41cccf8eb4bb580f33e47e10dcc\\transformed\\monitor-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b168428489ebc7a8cb14a9e8edb8c79a\\transformed\\interpolator-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\86101c4a703f326ab2f23318e76605b7\\transformed\\jetified-collection-jvm-1.4.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\faec63a70227b35987c5e7adb2a2c92a\\transformed\\documentfile-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\71d4845c8bb572d41666120594e51a9e\\transformed\\localbroadcastmanager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e2d8ba0d0e5789e4c581e71285454c2e\\transformed\\print-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9d99bb0161897bc15b802059ac95df59\\transformed\\jetified-annotation-jvm-1.9.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\db19d51b69c2fb5c7fcf9aa5c13afcf6\\transformed\\jetified-core-viewtree-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\72cc6753fa4177a84b965fae872bf494\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6035a68afda6fb5b46d4628c17318954\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fd63a71f62db038075b37610ce1a9732\\transformed\\jetified-kotlinx-coroutines-android-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7418c0f9ddf56f94a92ba5515701a1ad\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cd7ebb6f49580c290c07bbb0e1220b4f\\transformed\\jetified-kotlin-stdlib-1.9.24.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8efcb4507c13747c317a59818e3c567a\\transformed\\jetified-kotlin-stdlib-jdk8-1.8.22.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\133d48f8f8889ac3b908e49f919a3f13\\transformed\\jetified-armeabi_v7a_release-1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\55dd8974f8fce5e069700269b331e486\\transformed\\jetified-arm64_v8a_release-1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be9cc07023bd6b68a28620f839758b1d\\transformed\\jetified-x86_64_release-1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5f85d002ea58efc762a061e62326da21\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2cb5ac4f95b6a1b1bd6cf6271660a7bb\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a9bf485539fcbc00f49eecafbf96d820\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\34a358ca3ed7ed72330cd9e3f98713bb\\transformed\\jetified-jspecify-1.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.constraintlayout\\constraintlayout-solver\\2.0.1\\30988fe2d77f3fe3bf7551bb8a8b795fad7e7226\\constraintlayout-solver-2.0.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\10e13a2a2e67f0a9f61c5dee95486aec\\transformed\\jetified-relinker-1.4.5-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fca2dd3da34923956f848e9b72bea5ad\\transformed\\jetified-jsr305-3.0.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ff3affe4a16a70e1f615e03dec8da197\\transformed\\jetified-junit-4.12.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b4815acc0c297f7c9bef5360b55d90c3\\transformed\\jetified-hamcrest-integration-1.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\76cb08a0bfeeae40bd2a839083050ae1\\transformed\\jetified-hamcrest-library-1.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a59ad5bf9759bca8f078b85892008df0\\transformed\\jetified-hamcrest-core-1.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3bfac45ddb6e448c64657f7ed5fa3c17\\transformed\\jetified-kxml2-2.3.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ac4ce94d58a7cc004ae7c0974ff57a74\\transformed\\espresso-idling-resource-3.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46d8ea1492c656304aec1d83a351253c\\transformed\\jetified-javawriter-2.1.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9cb4f627dd73850689f143d4dc62bd66\\transformed\\jetified-javax.inject-1.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\platforms\\android-35\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\build-tools\\34.0.0\\core-lambda-stubs.jar" "-d" "G:\\New folder\\android\\app\\build\\tmp\\kotlin-classes\\release" "-jvm-target" "17" "-module-name" "app_release" "-no-jdk" "-no-reflect" "-no-stdlib" "G:\\New folder\\android\\app\\src\\main\\kotlin\\com\\moto_elmagic\\app\\MainActivity.kt" "G:\\New folder\\android\\app\\src\\main\\java\\io\\flutter\\plugins\\GeneratedPluginRegistrant.java"