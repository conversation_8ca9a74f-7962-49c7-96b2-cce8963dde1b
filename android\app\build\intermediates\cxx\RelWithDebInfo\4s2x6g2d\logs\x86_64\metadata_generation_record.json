[{"level_": 0, "message_": "Start JSON generation. Platform version: 21 min SDK version: x86_64", "file_": "C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt", "tag_": "release|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON G:\\New folder\\android\\app\\.cxx\\RelWithDebInfo\\4s2x6g2d\\x86_64\\android_gradle_build.json due to:", "file_": "C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt", "tag_": "release|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt", "tag_": "release|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'G:\\New folder\\android\\app\\.cxx\\RelWithDebInfo\\4s2x6g2d\\x86_64'", "file_": "C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt", "tag_": "release|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'G:\\New folder\\android\\app\\.cxx\\RelWithDebInfo\\4s2x6g2d\\x86_64'", "file_": "C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt", "tag_": "release|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HC:\\\\flutter\\\\packages\\\\flutter_tools\\\\gradle\\\\src\\\\main\\\\groovy\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=21\" ^\n  \"-DANDROID_PLATFORM=android-21\" ^\n  \"-DANDROID_ABI=x86_64\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86_64\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\ndk\\\\27.0.12077973\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\ndk\\\\27.0.12077973\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\ndk\\\\27.0.12077973\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=G:\\\\New folder\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\4s2x6g2d\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=G:\\\\New folder\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\4s2x6g2d\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_BUILD_TYPE=RelWithDebInfo\" ^\n  \"-BG:\\\\New folder\\\\android\\\\app\\\\.cxx\\\\RelWithDebInfo\\\\4s2x6g2d\\\\x86_64\" ^\n  -GNinja ^\n  -Wno-dev ^\n  --no-warn-unused-cli\n", "file_": "C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt", "tag_": "release|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HC:\\\\flutter\\\\packages\\\\flutter_tools\\\\gradle\\\\src\\\\main\\\\groovy\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=21\" ^\n  \"-DANDROID_PLATFORM=android-21\" ^\n  \"-DANDROID_ABI=x86_64\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86_64\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\ndk\\\\27.0.12077973\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\ndk\\\\27.0.12077973\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\ndk\\\\27.0.12077973\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=G:\\\\New folder\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\4s2x6g2d\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=G:\\\\New folder\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\4s2x6g2d\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_BUILD_TYPE=RelWithDebInfo\" ^\n  \"-BG:\\\\New folder\\\\android\\\\app\\\\.cxx\\\\RelWithDebInfo\\\\4s2x6g2d\\\\x86_64\" ^\n  -GNinja ^\n  -Wno-dev ^\n  --no-warn-unused-cli\n", "file_": "C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt", "tag_": "release|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt", "tag_": "release|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "G:\\New folder\\android\\app\\.cxx\\RelWithDebInfo\\4s2x6g2d\\x86_64\\compile_commands.json.bin existed but not G:\\New folder\\android\\app\\.cxx\\RelWithDebInfo\\4s2x6g2d\\x86_64\\compile_commands.json", "file_": "C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt", "tag_": "release|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt", "tag_": "release|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt", "tag_": "release|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]