{"logs": [{"outputFile": "com.moto_elmagic.app-mergeReleaseResources-76:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b93785a04fcd49acabbaa600426866db\\transformed\\jetified-play-services-base-18.3.0\\res\\values-zh-rCN\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,419,530,628,729,841,939,1027,1131,1228,1354,1465,1565,1669,1721,1774", "endColumns": "96,124,110,97,100,111,97,87,103,96,125,110,99,103,51,52,69", "endOffsets": "293,418,529,627,728,840,938,1026,1130,1227,1353,1464,1564,1668,1720,1773,1843"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4131,4232,4361,4476,4578,4683,4799,4901,5092,5200,5301,5431,5546,5650,5758,5814,5871", "endColumns": "100,128,114,101,104,115,101,91,107,100,129,114,103,107,55,56,73", "endOffsets": "4227,4356,4471,4573,4678,4794,4896,4988,5195,5296,5426,5541,5645,5753,5809,5866,5940"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37c9757273d42d5a9fb8fafd595f355e\\transformed\\appcompat-1.6.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,487,587,669,766,872,949,1024,1115,1208,1305,1401,1495,1588,1683,1775,1866,1957,2035,2131,2226,2321,2418,2514,2612,2760,10326", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "387,482,582,664,761,867,944,1019,1110,1203,1300,1396,1490,1583,1678,1770,1861,1952,2030,2126,2221,2316,2413,2509,2607,2755,2849,10400"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\df22b875ff6a90ec3ebad0ef728bb68b\\transformed\\browser-1.8.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "67,71,72,73", "startColumns": "4,4,4,4", "startOffsets": "6011,6314,6406,6507", "endColumns": "82,91,100,92", "endOffsets": "6089,6401,6502,6595"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f43509670beb1f53c1f6996445ca23a\\transformed\\core-1.16.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "38,39,40,41,42,43,44,129", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3197,3289,3390,3484,3578,3671,3765,10405", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "3284,3385,3479,3573,3666,3760,3856,10501"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\10d17790f6d6a95ec46bd68f81a8cd4a\\transformed\\material-1.9.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,443,513,590,681,787,860,922,999,1058,1117,1195,1256,1313,1369,1428,1486,1540,1625,1681,1739,1793,1858,1950,2024,2100,2222,2284,2346,2425,2475,2526,2592,2656,2725,2803,2864,2935,3002,3062,3141,3208,3291,3376,3450,3515,3591,3639,3703,3779,3857,3919,3983,4046,4126,4202,4280,4357,4411,4466", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,63,61,69,69,76,90,105,72,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,75,121,61,61,78,49,50,65,63,68,77,60,70,66,59,78,66,82,84,73,64,75,47,63,75,77,61,63,62,79,75,77,76,53,54,68", "endOffsets": "242,306,368,438,508,585,676,782,855,917,994,1053,1112,1190,1251,1308,1364,1423,1481,1535,1620,1676,1734,1788,1853,1945,2019,2095,2217,2279,2341,2420,2470,2521,2587,2651,2720,2798,2859,2930,2997,3057,3136,3203,3286,3371,3445,3510,3586,3634,3698,3774,3852,3914,3978,4041,4121,4197,4275,4352,4406,4461,4530"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,68,70,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2854,2918,2980,3050,3120,3861,3952,4058,6094,6237,6600,6659,6718,6796,6857,6914,6970,7029,7087,7141,7226,7282,7340,7394,7459,7551,7625,7701,7823,7885,7947,8026,8076,8127,8193,8257,8326,8404,8465,8536,8603,8663,8742,8809,8892,8977,9051,9116,9192,9240,9304,9380,9458,9520,9584,9647,9727,9803,9881,9958,10012,10137", "endLines": "5,33,34,35,36,37,45,46,47,68,70,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,126", "endColumns": "12,63,61,69,69,76,90,105,72,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,75,121,61,61,78,49,50,65,63,68,77,60,70,66,59,78,66,82,84,73,64,75,47,63,75,77,61,63,62,79,75,77,76,53,54,68", "endOffsets": "292,2913,2975,3045,3115,3192,3947,4053,4126,6151,6309,6654,6713,6791,6852,6909,6965,7024,7082,7136,7221,7277,7335,7389,7454,7546,7620,7696,7818,7880,7942,8021,8071,8122,8188,8252,8321,8399,8460,8531,8598,8658,8737,8804,8887,8972,9046,9111,9187,9235,9299,9375,9453,9515,9579,9642,9722,9798,9876,9953,10007,10062,10201"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\48bb13e58e49cdf1e6377472107b28a8\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-zh-rCN\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "94", "endOffsets": "293"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "4993", "endColumns": "98", "endOffsets": "5087"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\12a085f4af97d8500c9432a48145ea1b\\transformed\\preference-1.2.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,322,442,610,690", "endColumns": "65,80,69,119,167,79,76", "endOffsets": "166,247,317,437,605,685,762"}, "to": {"startLines": "66,69,125,127,130,131,132", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5945,6156,10067,10206,10506,10674,10754", "endColumns": "65,80,69,119,167,79,76", "endOffsets": "6006,6232,10132,10321,10669,10749,10826"}}]}]}