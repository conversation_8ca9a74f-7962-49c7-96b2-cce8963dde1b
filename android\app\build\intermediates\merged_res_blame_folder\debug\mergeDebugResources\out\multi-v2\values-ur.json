{"logs": [{"outputFile": "com.moto_elmagic.app-mergeDebugResources-77:/values-ur/values-ur.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37c9757273d42d5a9fb8fafd595f355e\\transformed\\appcompat-1.6.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,325,434,520,624,744,821,896,988,1082,1177,1271,1372,1466,1562,1656,1748,1840,1925,2033,2139,2241,2352,2453,2569,2734,2832", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "214,320,429,515,619,739,816,891,983,1077,1172,1266,1367,1461,1557,1651,1743,1835,1920,2028,2134,2236,2347,2448,2564,2729,2827,2913"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,423,529,638,724,828,948,1025,1100,1192,1286,1381,1475,1576,1670,1766,1860,1952,2044,2129,2237,2343,2445,2556,2657,2773,2938,11688", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "418,524,633,719,823,943,1020,1095,1187,1281,1376,1470,1571,1665,1761,1855,1947,2039,2124,2232,2338,2440,2551,2652,2768,2933,3031,11769"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\48bb13e58e49cdf1e6377472107b28a8\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-ur\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "147", "endOffsets": "342"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5472", "endColumns": "151", "endOffsets": "5619"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f43509670beb1f53c1f6996445ca23a\\transformed\\core-1.16.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,357,461,564,662,776", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "148,250,352,456,559,657,771,872"}, "to": {"startLines": "38,39,40,41,42,43,44,129", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3457,3555,3657,3759,3863,3966,4064,11774", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "3550,3652,3754,3858,3961,4059,4173,11870"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\df22b875ff6a90ec3ebad0ef728bb68b\\transformed\\browser-1.8.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,257,368", "endColumns": "100,100,110,106", "endOffsets": "151,252,363,470"}, "to": {"startLines": "67,71,72,73", "startColumns": "4,4,4,4", "startOffsets": "6792,7139,7240,7351", "endColumns": "100,100,110,106", "endOffsets": "6888,7235,7346,7453"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\10d17790f6d6a95ec46bd68f81a8cd4a\\transformed\\material-1.9.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,337,415,493,591,680,780,899,982,1047,1140,1210,1269,1359,1423,1492,1550,1619,1679,1743,1855,1914,1973,2028,2103,2226,2306,2390,2523,2605,2686,2773,2831,2887,2953,3028,3108,3193,3260,3335,3412,3476,3570,3640,3729,3822,3896,3971,4061,4117,4184,4268,4352,4414,4478,4541,4641,4748,4842,4951,5013,5073", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,77,77,77,97,88,99,118,82,64,92,69,58,89,63,68,57,68,59,63,111,58,58,54,74,122,79,83,132,81,80,86,57,55,65,74,79,84,66,74,76,63,93,69,88,92,73,74,89,55,66,83,83,61,63,62,99,106,93,108,61,59,79", "endOffsets": "254,332,410,488,586,675,775,894,977,1042,1135,1205,1264,1354,1418,1487,1545,1614,1674,1738,1850,1909,1968,2023,2098,2221,2301,2385,2518,2600,2681,2768,2826,2882,2948,3023,3103,3188,3255,3330,3407,3471,3565,3635,3724,3817,3891,3966,4056,4112,4179,4263,4347,4409,4473,4536,4636,4743,4837,4946,5008,5068,5148"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,68,70,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3036,3114,3192,3270,3368,4178,4278,4397,6893,7046,7458,7528,7587,7677,7741,7810,7868,7937,7997,8061,8173,8232,8291,8346,8421,8544,8624,8708,8841,8923,9004,9091,9149,9205,9271,9346,9426,9511,9578,9653,9730,9794,9888,9958,10047,10140,10214,10289,10379,10435,10502,10586,10670,10732,10796,10859,10959,11066,11160,11269,11331,11469", "endLines": "5,33,34,35,36,37,45,46,47,68,70,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,126", "endColumns": "12,77,77,77,97,88,99,118,82,64,92,69,58,89,63,68,57,68,59,63,111,58,58,54,74,122,79,83,132,81,80,86,57,55,65,74,79,84,66,74,76,63,93,69,88,92,73,74,89,55,66,83,83,61,63,62,99,106,93,108,61,59,79", "endOffsets": "304,3109,3187,3265,3363,3452,4273,4392,4475,6953,7134,7523,7582,7672,7736,7805,7863,7932,7992,8056,8168,8227,8286,8341,8416,8539,8619,8703,8836,8918,8999,9086,9144,9200,9266,9341,9421,9506,9573,9648,9725,9789,9883,9953,10042,10135,10209,10284,10374,10430,10497,10581,10665,10727,10791,10854,10954,11061,11155,11264,11326,11386,11544"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b93785a04fcd49acabbaa600426866db\\transformed\\jetified-play-services-base-18.3.0\\res\\values-ur\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,457,579,686,822,945,1053,1153,1301,1407,1575,1699,1841,2006,2065,2128", "endColumns": "103,159,121,106,135,122,107,99,147,105,167,123,141,164,58,62,83", "endOffsets": "296,456,578,685,821,944,1052,1152,1300,1406,1574,1698,1840,2005,2064,2127,2211"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4480,4588,4752,4878,4989,5129,5256,5368,5624,5776,5886,6058,6186,6332,6501,6564,6631", "endColumns": "107,163,125,110,139,126,111,103,151,109,171,127,145,168,62,66,87", "endOffsets": "4583,4747,4873,4984,5124,5251,5363,5467,5771,5881,6053,6181,6327,6496,6559,6626,6714"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\12a085f4af97d8500c9432a48145ea1b\\transformed\\preference-1.2.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,266,344,483,652,734", "endColumns": "72,87,77,138,168,81,75", "endOffsets": "173,261,339,478,647,729,805"}, "to": {"startLines": "66,69,125,127,130,131,132", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6719,6958,11391,11549,11875,12044,12126", "endColumns": "72,87,77,138,168,81,75", "endOffsets": "6787,7041,11464,11683,12039,12121,12197"}}]}]}