# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.22"
  }
  digests {
    sha256: "A\230\260\352\360\220\244\362[o~ZYX\037C\024\272\214\237l\321\321>\351\323H\346^\330\367\a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.9.24"
  }
  digests {
    sha256: "\205\213\220&\226\332\234\365\205\253\235\230\377\301\302q\"i\202\203T\337\351\020~7\021\260\204\243dh"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.8.22"
  }
  digests {
    sha256: "\005_\\\262B\207\372\020a\000\231Z{G\253\222\022k\201\3502\350u\365\372,\360\275Ui=\v"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.6.1"
  }
  digests {
    sha256: "~\245W;\223\253\253\323\27521$Q\306\352H\246b\260:\024\r\332\201\256\276uwj \244\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.9.3"
  }
  digests {
    sha256: "~E\322\002=\301}q_\377\363\334W\222\367\230d,J\341\034\224\200\006\261u*\271\355\332W\a"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.9.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.9.1"
  }
  digests {
    sha256: "\03649\027\353\362{\251o\344\334R\261\312\327\3752\2678\373\3065[\266\315[;0]r\022\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.4.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.4.2"
  }
  digests {
    sha256: "\230L\351\275x\000U\352\373\212\020Z\312?QF\206\277{\033br\354|dZ\230\316@\374}\264"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.4.2"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.16.0"
  }
  digests {
    sha256: "k\360=9\333\343tJ\314\342\'\323\266\2277L6%\252\341\002_\276\310\255\237\327\275X\274\3441"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.1"
  }
  digests {
    sha256: "k\324\307\307Go\202`\315;\333\270\021\203X>\223\374\237y\f\'\336\247\3341A\201\313\370z\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-viewtree"
    version: "1.0.0"
  }
  digests {
    sha256: "\334\033g\215X\353\317+\372\025\207\276h\377\202f\224\316=\"\022Q\271\3570\324\324\263b\227\346\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.8.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-android"
    version: "2.8.7"
  }
  digests {
    sha256: "\270\353\231}\3244\327\016\271|\275m\023\225\2663|gqq|z\315Y\361\034\bS\363\206\002\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.8.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-jvm"
    version: "2.8.7"
  }
  digests {
    sha256: "a\310s\2472|\224n\3003\303\020\273\230\363\371.\352\274\355\340\341\245 \n\270\241\211d\203\307\277"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.8.7"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.8.7"
  }
  digests {
    sha256: "p64RZg\261\306\276\252\253O\230\r\372\v\254\337~\024=\v]Q\365\254\r\224\004@\002\220"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.8.7"
  }
  digests {
    sha256: "\327\206\345\346\367\233\217\253\002o\t\242\324\256=\341\327(u\367dZk{\344\217\035\232\263\204sK"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.8.7"
  }
  digests {
    sha256: "\266\347\205\350S+\025Y!1\005c\251\017\322b\265\332\317id\256\352l\273\344\022\254\035\245\224`"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.8.7"
  }
  digests {
    sha256: "b\340v\242\336-e\n#\277yIT\031\370\2445z\330\351\262\215\200\023\030\320\374\335n5\372\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing-ktx"
    version: "1.2.0"
  }
  digests {
    sha256: "\303?\234\275\223\036a\220\3128\252\t\277\212z\212\0319\035K\017\267\247`ZkY\362\324%\200\321"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.8.7"
  }
  digests {
    sha256: "C\322\212lm\251\301\313r\277\265\314\033Q\032y7\262\333\213y\325/7\320\267\361Ly\260\220\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-android"
    version: "2.8.7"
  }
  digests {
    sha256: "\347\347\004?f\260wcuc\377VJ\177\306\316\344a\322\204\240\362H\\\005\n!C\253\275\211 "
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.8.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx-android"
    version: "2.8.7"
  }
  digests {
    sha256: "\263\323\234]\272^ ~\364\307\235I\032vP\001\224\305\352ht\025\177\005\241_\001y,\f\304\347"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.8.7"
  }
  digests {
    sha256: "\032\316\273P\337/\f\a\363y\325\342:(\367yOp\357\262\266\3104\254\353\350\303\224xN\346\324"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.8.7"
  }
  digests {
    sha256: "do*B\322\210K\0020\217\261\2307\b\236&i\203_\342\034\246\245\266\006f\325\353\224\320\201\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.16.0"
  }
  digests {
    sha256: "\027f\333\330/d\241-\3155\236\313o\025\363\3175\333Mf\322)a\247\305\033/\374dh1L"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.1"
  }
  digests {
    sha256: "\320\344\002\3541\362@(\241\334~\266\240\243\371\331c\\\024Y9,\32749cC\267=g9H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jspecify"
    artifactId: "jspecify"
    version: "1.0.0"
  }
  digests {
    sha256: "\037\255nk\347Uw\201\344\3237)\324\232\341\315\310\375\332o\344w\273\f\306\214\343Q\352\375\373\253"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.9.3"
  }
  digests {
    sha256: "\342\3324\366\214\254Uy\206(\001\230\345\301Tk\212$Ri\260P\261\314\220\265i\026\333\366\222\001"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.6.1"
  }
  digests {
    sha256: "\333\221]\277I5xc\336\026i\377\237\335\216\220\b\326_\343W\257l\316\232\3460C\255_f\027"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.2.0"
  }
  digests {
    sha256: "\363\032\006\301P\354\2600s\365Zo{\vt\242@\246\250\327\'\301L\347g&\320 W\r\372\214"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.2.0"
  }
  digests {
    sha256: "\177\372MFM\235\262Y\374\240\315\265\017\275J\266=hr\274\332YF\213\237uUPL}Z\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.7.1"
  }
  digests {
    sha256: "}\336\276\235\203\n\004\22673\334\263@\312\264\272\301\346\235\374\247\227\246\325\232\247\274V(!P\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.1.0"
  }
  digests {
    sha256: "\250;Y|\322@\235\301w\367\3712\367\304\b.\367\265P\213i\266\304\272q`~u\333\331\231q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment-ktx"
    version: "1.7.1"
  }
  digests {
    sha256: "\217\254\271\a\030\317R^\257\376m\033\326\024]\377\025mj\345\035<\037\202\002\315\005\216\370\201\366*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.9.0"
  }
  digests {
    sha256: "l\3025\231y&\236M\236\335\316}\204h-+\260j5\241N\334\350\006\277\r\246\350\324\323\030\006"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.36.0"
  }
  digests {
    sha256: "wD\016\'\v\v\311\242I\220<Z\al6\247\"\304\210l\244\364&u\362\220:\034S\355a\245"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.0.1"
  }
  digests {
    sha256: "\354\025\265\324\242\357\360x\210\274\024\231\316.,n\376$\300\355`\314W\260\214\235\304\266\375<!\211"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-solver"
    version: "2.0.1"
  }
  digests {
    sha256: "\26272\355\2735\021\3317\376\241\377\357\004{\016l\000\033P\301\222\037\r\225\237\303\204\327\006\354j"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.1.0"
  }
  digests {
    sha256: "\360\322\265\246}\n\221\356\033\034s\357+cj\201\363V9%\335\321Z\035N\034A\354(\336zO"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.4.1"
  }
  digests {
    sha256: "6\322\215\236\303:\214d18B\274\351\234\225sm\245\262zk:Q69\005\r\350/\aW&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.0.0"
  }
  digests {
    sha256: "\351\\\0001\324\314$|\324\201\226\306(~X\322\316\345M\234y\270Z\376\247\311\t 3\002u\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "armeabi_v7a_release"
    version: "1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa"
  }
  digests {
    sha256: "\f-`\'S\312\304\203\264\200*\266\235\270-\303\302\320E\321w\334\031\\w\325W4#U~\020"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "arm64_v8a_release"
    version: "1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa"
  }
  digests {
    sha256: "_\357\332\b\027\243\342\242J\367\210\016\373\313\252\317\324\\\241g\332\223F\247\3460\231\317x\221\324\312"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "x86_64_release"
    version: "1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa"
  }
  digests {
    sha256: "\303+\323\253W\302\321\004\037\327\356\207\005#\272\364\241\252\225\003\020\314 \271\231\377\fn/`z\362"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "org.microg"
    artifactId: "safe-parcel"
    version: "1.7.0"
  }
  digests {
    sha256: "b\237^\277\333Px\326y\024!\2434\232\331\267\321\251\377m\b\314\023\266\241{\377\276\217\227V\330"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "flutter_embedding_release"
    version: "1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa"
  }
  digests {
    sha256: "A\345\035W%u\025{\310\225L{\023\261\2373\035;J\304\2469\032B\230\216Q\327+\177\3146"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.window"
    artifactId: "window-java"
    version: "1.2.0"
  }
  digests {
    sha256: "\343\336\373^\343\205wWP]z\326bu\334:\v\001Ai0\033\211\326\344\356\300;\016E\274\253"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.window"
    artifactId: "window"
    version: "1.2.0"
  }
  digests {
    sha256: "\2504\302\027\331s\021\'wS=\257\022\201\332\263;\327x\372\335-f\276\2749\265\376\201\250\220\254"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.window.extensions.core"
    artifactId: "core"
    version: "1.0.0"
  }
  digests {
    sha256: "1\000\205K-\300\336\\M\210\264~\034]\263\b\001w\f\202\035\312(\030\224C\267bO@\b\256"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.getkeepsafe.relinker"
    artifactId: "relinker"
    version: "1.4.5"
  }
  digests {
    sha256: "\260;M\021:\237\357y@\n\350T\301wW\302\221W%\346\377\321\326\021\026\352\332\353V\0237J"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-core"
    version: "1.4.1"
  }
  digests {
    sha256: "lw\006\210\006\317%\233\272\247\355\225Y5\277,\321m\301\003\376\263/z\343\325X#\374\237\317\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures-ktx"
    version: "1.1.0"
  }
  digests {
    sha256: "\031h\277R\003\2368cj\246\361\024\315\027\327%i\031\321\350\231t\027qo\357\235\035\241\362M\205"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.3.7"
  }
  digests {
    sha256: "\016\217\0302&l[\006g\255=;\020\230\346$\344\232\t\aT\223\240\024\247\350\212\360\037\323\n\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.auto.value"
    artifactId: "auto-value-annotations"
    version: "1.6.3"
  }
  digests {
    sha256: "\016\225\037\356\2141\366\002p\274FU:\205\206\000\033{\223\333\261*\354\0067:\251\232\025\003\222\300"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-camera2"
    version: "1.4.1"
  }
  digests {
    sha256: "\0272S\3552\302\366\016w\b\032\253r\346\035\274\304\347g\311\376V\313\271\270\217v+w%\351\233"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-lifecycle"
    version: "1.4.1"
  }
  digests {
    sha256: "\203$\255\262\300G\220_\377\275\020\245\275\227\210\030\025yCa\233\221\374\364\020\336\224\275\227\266\031\251"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-video"
    version: "1.4.1"
  }
  digests {
    sha256: "\233\0333\271J\227\b\204\215\361\242\271A\367\255=\216\230+i&4\247y\3747\"\254r6\001\016"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "33.4.0-android"
  }
  digests {
    sha256: "\3435\315\026xBo\025\340J\344\366#\305\213L\027er\327%\214n\246N47\315\300\036W\a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.2"
  }
  digests {
    sha256: "\212\217\201\317\2335\236?m\372i\032\036wi\205\300a\357/\"<\233,\200u>\033E\216\200d"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.checkerframework"
    artifactId: "checker-qual"
    version: "3.43.0"
  }
  digests {
    sha256: "?\274.\230\360XT\303\337\026\337\232\272\251U\271\033\025\263\354\2543b2\b\355d$d\016\360\366"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.j2objc"
    artifactId: "j2objc-annotations"
    version: "3.0.0"
  }
  digests {
    sha256: "\210$\025sF}\334\244O\375Mt\252\004\302\273\375\021\277|\027\340\303B\311L\235\347\247\n|d"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.apache.tika"
    artifactId: "tika-core"
    version: "3.1.0"
  }
  digests {
    sha256: "#\2537\350\346\315\227\374w\367\256q\374\031\357\022\256K\221\267\246\251\022\r\360E\2721\374\327\320C"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.slf4j"
    artifactId: "slf4j-api"
    version: "2.0.16"
  }
  digests {
    sha256: "\241%x\335\341\272\000\275\233\201m8\212\v\207\231(\320\v\253<\203\302@\367\001;\364\031lW\232"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "commons-io"
    artifactId: "commons-io"
    version: "2.18.0"
  }
  digests {
    sha256: "\363\312\017\215c\304\016#\245mT\020\034`\325\355\356\023kB\330K\373\205\274yc\t1\t\317\213"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.media"
    artifactId: "media"
    version: "1.1.0"
  }
  digests {
    sha256: "\337\2735\272\247\037\263\305\272;\367\363}\006\314~\233\346\203\f\'n$0\032RY\"\246\341C\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.12.0"
  }
  digests {
    sha256: "M7\177Q\310K\265\264\345`;\336\345\376\177\374\232\266\267\256\344\356\016\037b\306\372\335\204\305\372\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-location"
    version: "21.2.0"
  }
  digests {
    sha256: "\274\260mi\312\212D\263X8\216\f \345\017\341ZX\265\223\244\313\231\310\315$\366\340\021[\252\326"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.3.0"
  }
  digests {
    sha256: "\224\006jF\004~=Y>\266R8>wg\340c\003\205\354\327Q3\346\214!\022J\251k\215\302"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.3.0"
  }
  digests {
    sha256: "l\021\256>\262\335\177\0277?\221\234LUzp\344\317\211\033\300\311\266i&\240\246D]eCR"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.1.0"
  }
  digests {
    sha256: "\326\005u\352\343\223P\346#HX\274\235}wSupz\350*hNl\257\177>A\241.%\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth"
    version: "21.0.0"
  }
  digests {
    sha256: "\002\271\240\234N\f\261z\r\211<\277@\3741\323p\2262a\233\v\375%\036\\Q\343\347\256\327\273"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-api-phone"
    version: "18.0.2"
  }
  digests {
    sha256: "\277\3378\016t\000\251\331P\313\244\336\334\025+\033\316aJ\20750\t\350Q\bt(\222|\302@"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-base"
    version: "18.0.10"
  }
  digests {
    sha256: "\330\277\362\311\215#\2263\373U\002)\345\'w\224%\277\265\037\360K\226\254\017\022\323\254\272\v\037B"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-fido"
    version: "20.0.1"
  }
  digests {
    sha256: "\263Ro\f\256\332\251lRl\304\311\272\224\262)\332\325\257\343\0372\364ol\326h\2406$\375B"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.test"
    artifactId: "runner"
    version: "1.2.0"
  }
  digests {
    sha256: "S\207\340\021\026z<\215\240\215\231\265\325\222H\300\342\332\203\223\027\264\216\277 .1\334\037y\036\301"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.test"
    artifactId: "monitor"
    version: "1.2.0"
  }
  digests {
    sha256: "\374\227\312?\000\370\3120\267\325\026\177\275\2076u`H\342\314O\216\222\334\211\021\006u\032[\256\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "junit"
    artifactId: "junit"
    version: "4.12"
  }
  digests {
    sha256: "Yr\037\b\005\342#\330K\220gx\207\331\377V}\3054\327\305\002\312\220<\f+\027\360\\\021j"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.hamcrest"
    artifactId: "hamcrest-core"
    version: "1.3"
  }
  digests {
    sha256: "f\375\357\221\351s\223H\337z\tj\243\204\245h_N\207U\204\314\350\223\206\247\244rQ\304\330\351"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "net.sf.kxml"
    artifactId: "kxml2"
    version: "2.3.0"
  }
  digests {
    sha256: "\362d\335\237y\241\375\341\f\345\354\3052!\357\362K\344\3113\034\203\v}R\362\360\212{c=\342"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.test"
    artifactId: "rules"
    version: "1.2.0"
  }
  digests {
    sha256: "$\275q\021\340\333\221\264\245\366\325\303\343\350\226\230X\r\311\r)\'=\004\247u\273\177\347\302\247a"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.test.espresso"
    artifactId: "espresso-core"
    version: "3.2.0"
  }
  digests {
    sha256: "\276\264q,% \301\3320\254\037%Phq\361n\245\270>\346\206\354\345\242Xv\235\361\240\036\025"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.test.espresso"
    artifactId: "espresso-idling-resource"
    version: "3.2.0"
  }
  digests {
    sha256: "\301\240EO\351W\210\022+\246R\303\354\377~\3058\307\342}\342\006\256\331p\362\200\237\270\t\r\t"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup"
    artifactId: "javawriter"
    version: "2.1.1"
  }
  digests {
    sha256: "\366\231\202=\000\201\366\234\273gl\030E\352\".\n\332y\274\210\245>]\"\330\275\002\323(\365~"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.hamcrest"
    artifactId: "hamcrest-library"
    version: "1.3"
  }
  digests {
    sha256: "q\035dR/\236\304\020\230;\323\020\223B\226\332\023K\344%J\022P\200\240An\301x\337\255\034"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.hamcrest"
    artifactId: "hamcrest-integration"
    version: "1.3"
  }
  digests {
    sha256: "p\364\030\357\273PlQU\332_\232Z3&.\240\212\236M\177\352\030j\251\001\\A\247\"J\302"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-android"
    version: "1.1.3"
  }
  digests {
    sha256: "z\235o`!\215w_\3549\250X@C5\235\373jcZa\326\206\027\252n\230&\330\232\001;"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core-android"
    version: "1.1.3"
  }
  digests {
    sha256: "\260\257x\371\b\203@z\346?\321HW\304\346.w\037>\236?\202\340\227q[M\2518\215\340\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-parcelize-runtime"
    version: "1.9.22"
  }
  digests {
    sha256: "\360\a\1775\354\202\216\024\212\311-\fj\241A\2472\265\353zJ9\026y\256\357\245\237\251\241\245\314"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-android-extensions-runtime"
    version: "1.9.22"
  }
  digests {
    sha256: "\360\0057\307\002>`I3\366\251V;\033\263\270>a\373\375,\303\320\351\220\rak\253hb\244"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core-okio"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core-okio-jvm"
    version: "1.1.3"
  }
  digests {
    sha256: ")\025\006\264\374\343\230$\227\223\343\' v5\275M\362Q\201\331S\230\b\327\030\032J\275\bu\365"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.4.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.4.0"
  }
  digests {
    sha256: "\0019\354zPm\273\325L\255b)\033\001\234\270PSK\340\227\310\306l\020\000\325\373\350\355\357>"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-android"
    version: "1.1.3"
  }
  digests {
    sha256: "\220\333\\\004\261Ho\255*\017\342\220 \3769D\300\372\f]6\361A\036\336\241\231\000t*E\023"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-core"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-core-jvm"
    version: "1.1.3"
  }
  digests {
    sha256: "\3139\341\";C$\3270\305s\242\274\352:50\236\275\223X\373\3460\211\227\005\243\373\312\312\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-proto"
    version: "1.1.3"
  }
  digests {
    sha256: "chja/9\270\312`;\353\3437mU\373l\236\342\212JQs\357\244\234\211\313\314\250\241\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-external-protobuf"
    version: "1.1.3"
  }
  digests {
    sha256: "\374\263\363s\317t4&\310^\273\r<\306D\256\372\362o9\267\372U\273\220#\215A\266\211\354\247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.preference"
    artifactId: "preference"
    version: "1.2.1"
  }
  digests {
    sha256: "@\312\212\337\333~\377\266\037\254\263\233\331\312./:@\321\006t;\f\326\334\236!\350\276\335O\205"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.slidingpanelayout"
    artifactId: "slidingpanelayout"
    version: "1.2.0"
  }
  digests {
    sha256: "_S3\233\342\244\371\n\232\276\243W\035\325\236p\250\244\236\177\025\335\202\227J8\230\264e.\207\024"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.browser"
    artifactId: "browser"
    version: "1.8.0"
  }
  digests {
    sha256: "-ag\263;\366\240d\303u\001P\025Cp#I\350P\254b\3072\321\357\244\347\020cgTV"
  }
  repo_index {
  }
}
library {
  digests {
    sha256: "\025\032l\254\305\016:Y!M\323\337\352\312\202X\225L\324\346\254C\361\306\273x\377c\306\241\r\213"
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 3
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 0
}
library_dependencies {
  library_index: 3
  library_dep_index: 1
}
library_dependencies {
  library_index: 4
  library_dep_index: 5
  library_dep_index: 6
  library_dep_index: 48
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 41
  library_dep_index: 51
  library_dep_index: 52
  library_dep_index: 54
  library_dep_index: 55
  library_dep_index: 56
  library_dep_index: 17
  library_dep_index: 35
  library_dep_index: 60
  library_dep_index: 42
  library_dep_index: 1
  library_dep_index: 48
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 17
  library_dep_index: 35
  library_dep_index: 40
  library_dep_index: 44
  library_dep_index: 42
  library_dep_index: 33
  library_dep_index: 1
  library_dep_index: 47
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 7
  library_dep_index: 1
}
library_dependencies {
  library_index: 8
  library_dep_index: 9
}
library_dependencies {
  library_index: 9
  library_dep_index: 6
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 10
}
library_dependencies {
  library_index: 10
  library_dep_index: 8
  library_dep_index: 8
}
library_dependencies {
  library_index: 11
  library_dep_index: 6
  library_dep_index: 12
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 45
  library_dep_index: 1
  library_dep_index: 46
  library_dep_index: 41
  library_dep_index: 1
}
library_dependencies {
  library_index: 12
  library_dep_index: 1
}
library_dependencies {
  library_index: 13
  library_dep_index: 6
  library_dep_index: 14
}
library_dependencies {
  library_index: 15
  library_dep_index: 1
  library_dep_index: 1
}
library_dependencies {
  library_index: 16
  library_dep_index: 6
}
library_dependencies {
  library_index: 17
  library_dep_index: 18
}
library_dependencies {
  library_index: 18
  library_dep_index: 6
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 44
  library_dep_index: 1
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 35
  library_dep_index: 40
  library_dep_index: 30
  library_dep_index: 39
  library_dep_index: 37
}
library_dependencies {
  library_index: 19
  library_dep_index: 6
}
library_dependencies {
  library_index: 20
  library_dep_index: 6
  library_dep_index: 19
}
library_dependencies {
  library_index: 21
  library_dep_index: 22
}
library_dependencies {
  library_index: 22
  library_dep_index: 6
  library_dep_index: 1
  library_dep_index: 23
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 17
  library_dep_index: 37
  library_dep_index: 35
  library_dep_index: 39
  library_dep_index: 40
}
library_dependencies {
  library_index: 23
  library_dep_index: 24
}
library_dependencies {
  library_index: 24
  library_dep_index: 2
  library_dep_index: 25
  library_dep_index: 0
}
library_dependencies {
  library_index: 25
  library_dep_index: 26
  library_dep_index: 24
  library_dep_index: 23
}
library_dependencies {
  library_index: 26
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 0
}
library_dependencies {
  library_index: 27
  library_dep_index: 6
  library_dep_index: 21
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 17
  library_dep_index: 35
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 37
}
library_dependencies {
  library_index: 28
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 1
  library_dep_index: 23
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 17
  library_dep_index: 35
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 37
}
library_dependencies {
  library_index: 29
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 1
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 17
  library_dep_index: 35
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 37
}
library_dependencies {
  library_index: 30
  library_dep_index: 29
  library_dep_index: 1
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 17
  library_dep_index: 37
  library_dep_index: 35
  library_dep_index: 39
  library_dep_index: 40
}
library_dependencies {
  library_index: 31
  library_dep_index: 6
  library_dep_index: 17
  library_dep_index: 32
  library_dep_index: 1
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 17
  library_dep_index: 35
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 37
}
library_dependencies {
  library_index: 32
  library_dep_index: 6
  library_dep_index: 33
}
library_dependencies {
  library_index: 33
  library_dep_index: 6
  library_dep_index: 34
}
library_dependencies {
  library_index: 34
  library_dep_index: 33
  library_dep_index: 1
  library_dep_index: 33
}
library_dependencies {
  library_index: 35
  library_dep_index: 36
}
library_dependencies {
  library_index: 36
  library_dep_index: 6
  library_dep_index: 1
  library_dep_index: 26
  library_dep_index: 23
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 17
  library_dep_index: 37
  library_dep_index: 39
  library_dep_index: 40
}
library_dependencies {
  library_index: 37
  library_dep_index: 38
}
library_dependencies {
  library_index: 38
  library_dep_index: 6
  library_dep_index: 17
  library_dep_index: 1
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 17
  library_dep_index: 35
  library_dep_index: 39
  library_dep_index: 40
}
library_dependencies {
  library_index: 39
  library_dep_index: 35
  library_dep_index: 1
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 17
  library_dep_index: 37
  library_dep_index: 35
  library_dep_index: 40
}
library_dependencies {
  library_index: 40
  library_dep_index: 6
  library_dep_index: 41
  library_dep_index: 29
  library_dep_index: 35
  library_dep_index: 42
  library_dep_index: 1
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 17
  library_dep_index: 35
  library_dep_index: 39
  library_dep_index: 37
}
library_dependencies {
  library_index: 41
  library_dep_index: 6
  library_dep_index: 11
  library_dep_index: 1
  library_dep_index: 11
  library_dep_index: 1
}
library_dependencies {
  library_index: 42
  library_dep_index: 6
  library_dep_index: 19
  library_dep_index: 21
  library_dep_index: 1
  library_dep_index: 43
}
library_dependencies {
  library_index: 43
  library_dep_index: 42
  library_dep_index: 1
  library_dep_index: 42
}
library_dependencies {
  library_index: 44
  library_dep_index: 6
  library_dep_index: 13
  library_dep_index: 32
  library_dep_index: 14
}
library_dependencies {
  library_index: 45
  library_dep_index: 6
  library_dep_index: 8
}
library_dependencies {
  library_index: 47
  library_dep_index: 5
  library_dep_index: 41
  library_dep_index: 37
  library_dep_index: 39
  library_dep_index: 43
  library_dep_index: 1
  library_dep_index: 5
}
library_dependencies {
  library_index: 48
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 4
}
library_dependencies {
  library_index: 49
  library_dep_index: 6
  library_dep_index: 11
  library_dep_index: 8
}
library_dependencies {
  library_index: 50
  library_dep_index: 49
  library_dep_index: 16
  library_dep_index: 8
}
library_dependencies {
  library_index: 51
  library_dep_index: 6
}
library_dependencies {
  library_index: 52
  library_dep_index: 6
  library_dep_index: 11
  library_dep_index: 53
}
library_dependencies {
  library_index: 53
  library_dep_index: 6
  library_dep_index: 11
  library_dep_index: 8
}
library_dependencies {
  library_index: 54
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 55
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 54
}
library_dependencies {
  library_index: 56
  library_dep_index: 5
  library_dep_index: 6
  library_dep_index: 12
  library_dep_index: 8
  library_dep_index: 41
  library_dep_index: 29
  library_dep_index: 17
  library_dep_index: 35
  library_dep_index: 40
  library_dep_index: 57
  library_dep_index: 44
  library_dep_index: 42
  library_dep_index: 58
  library_dep_index: 1
  library_dep_index: 59
}
library_dependencies {
  library_index: 57
  library_dep_index: 6
  library_dep_index: 11
  library_dep_index: 29
  library_dep_index: 35
  library_dep_index: 8
}
library_dependencies {
  library_index: 58
  library_dep_index: 6
  library_dep_index: 11
  library_dep_index: 53
}
library_dependencies {
  library_index: 59
  library_dep_index: 47
  library_dep_index: 10
  library_dep_index: 41
  library_dep_index: 56
  library_dep_index: 30
  library_dep_index: 39
  library_dep_index: 43
  library_dep_index: 1
  library_dep_index: 56
}
library_dependencies {
  library_index: 60
  library_dep_index: 6
}
library_dependencies {
  library_index: 61
  library_dep_index: 62
  library_dep_index: 6
  library_dep_index: 4
  library_dep_index: 63
  library_dep_index: 64
  library_dep_index: 65
  library_dep_index: 11
  library_dep_index: 52
  library_dep_index: 67
  library_dep_index: 12
  library_dep_index: 56
  library_dep_index: 17
  library_dep_index: 72
  library_dep_index: 73
  library_dep_index: 49
  library_dep_index: 74
}
library_dependencies {
  library_index: 63
  library_dep_index: 6
}
library_dependencies {
  library_index: 64
  library_dep_index: 6
  library_dep_index: 11
  library_dep_index: 53
  library_dep_index: 8
}
library_dependencies {
  library_index: 65
  library_dep_index: 4
  library_dep_index: 11
  library_dep_index: 66
}
library_dependencies {
  library_index: 67
  library_dep_index: 11
  library_dep_index: 8
  library_dep_index: 68
}
library_dependencies {
  library_index: 68
  library_dep_index: 6
  library_dep_index: 11
  library_dep_index: 69
  library_dep_index: 57
  library_dep_index: 70
  library_dep_index: 71
}
library_dependencies {
  library_index: 69
  library_dep_index: 6
}
library_dependencies {
  library_index: 70
  library_dep_index: 6
}
library_dependencies {
  library_index: 71
  library_dep_index: 6
}
library_dependencies {
  library_index: 72
  library_dep_index: 6
  library_dep_index: 11
  library_dep_index: 53
  library_dep_index: 8
}
library_dependencies {
  library_index: 73
  library_dep_index: 6
  library_dep_index: 11
  library_dep_index: 8
}
library_dependencies {
  library_index: 74
  library_dep_index: 6
  library_dep_index: 56
  library_dep_index: 72
  library_dep_index: 11
  library_dep_index: 8
}
library_dependencies {
  library_index: 79
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 31
  library_dep_index: 17
  library_dep_index: 56
  library_dep_index: 6
  library_dep_index: 33
  library_dep_index: 11
  library_dep_index: 80
  library_dep_index: 83
}
library_dependencies {
  library_index: 80
  library_dep_index: 11
  library_dep_index: 81
  library_dep_index: 1
  library_dep_index: 23
  library_dep_index: 81
}
library_dependencies {
  library_index: 81
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 82
  library_dep_index: 1
  library_dep_index: 26
  library_dep_index: 80
}
library_dependencies {
  library_index: 82
  library_dep_index: 6
  library_dep_index: 1
}
library_dependencies {
  library_index: 84
  library_dep_index: 6
  library_dep_index: 12
  library_dep_index: 13
  library_dep_index: 85
  library_dep_index: 11
  library_dep_index: 86
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 33
  library_dep_index: 87
  library_dep_index: 14
  library_dep_index: 1
  library_dep_index: 26
  library_dep_index: 88
  library_dep_index: 89
  library_dep_index: 90
}
library_dependencies {
  library_index: 85
  library_dep_index: 13
  library_dep_index: 1
  library_dep_index: 23
}
library_dependencies {
  library_index: 86
  library_dep_index: 6
}
library_dependencies {
  library_index: 88
  library_dep_index: 6
  library_dep_index: 84
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 33
  library_dep_index: 87
  library_dep_index: 14
  library_dep_index: 84
  library_dep_index: 89
  library_dep_index: 90
}
library_dependencies {
  library_index: 89
  library_dep_index: 84
  library_dep_index: 13
  library_dep_index: 85
  library_dep_index: 11
  library_dep_index: 21
  library_dep_index: 34
  library_dep_index: 87
  library_dep_index: 14
  library_dep_index: 26
  library_dep_index: 88
  library_dep_index: 84
  library_dep_index: 90
}
library_dependencies {
  library_index: 90
  library_dep_index: 6
  library_dep_index: 84
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 87
  library_dep_index: 88
  library_dep_index: 84
  library_dep_index: 89
}
library_dependencies {
  library_index: 91
  library_dep_index: 92
  library_dep_index: 14
  library_dep_index: 93
  library_dep_index: 94
  library_dep_index: 62
  library_dep_index: 95
}
library_dependencies {
  library_index: 96
  library_dep_index: 97
  library_dep_index: 98
}
library_dependencies {
  library_index: 99
  library_dep_index: 11
  library_dep_index: 8
}
library_dependencies {
  library_index: 100
  library_dep_index: 62
}
library_dependencies {
  library_index: 101
  library_dep_index: 102
  library_dep_index: 103
  library_dep_index: 104
  library_dep_index: 1
  library_dep_index: 26
  library_dep_index: 23
}
library_dependencies {
  library_index: 102
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 56
  library_dep_index: 103
  library_dep_index: 104
}
library_dependencies {
  library_index: 103
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 56
}
library_dependencies {
  library_index: 104
  library_dep_index: 103
}
library_dependencies {
  library_index: 105
  library_dep_index: 56
  library_dep_index: 57
  library_dep_index: 106
  library_dep_index: 107
  library_dep_index: 102
  library_dep_index: 103
  library_dep_index: 108
  library_dep_index: 104
}
library_dependencies {
  library_index: 106
  library_dep_index: 102
  library_dep_index: 103
  library_dep_index: 104
}
library_dependencies {
  library_index: 107
  library_dep_index: 8
  library_dep_index: 102
  library_dep_index: 103
  library_dep_index: 104
}
library_dependencies {
  library_index: 108
  library_dep_index: 102
  library_dep_index: 103
  library_dep_index: 104
}
library_dependencies {
  library_index: 109
  library_dep_index: 6
  library_dep_index: 110
  library_dep_index: 111
  library_dep_index: 113
}
library_dependencies {
  library_index: 110
  library_dep_index: 6
}
library_dependencies {
  library_index: 111
  library_dep_index: 112
}
library_dependencies {
  library_index: 114
  library_dep_index: 109
}
library_dependencies {
  library_index: 115
  library_dep_index: 109
  library_dep_index: 116
  library_dep_index: 117
  library_dep_index: 118
  library_dep_index: 119
  library_dep_index: 120
  library_dep_index: 93
}
library_dependencies {
  library_index: 119
  library_dep_index: 112
}
library_dependencies {
  library_index: 120
  library_dep_index: 119
}
library_dependencies {
  library_index: 121
  library_dep_index: 122
}
library_dependencies {
  library_index: 122
  library_dep_index: 6
  library_dep_index: 123
  library_dep_index: 127
  library_dep_index: 129
  library_dep_index: 1
  library_dep_index: 23
  library_dep_index: 123
  library_dep_index: 127
  library_dep_index: 131
  library_dep_index: 133
  library_dep_index: 135
  library_dep_index: 136
}
library_dependencies {
  library_index: 123
  library_dep_index: 124
}
library_dependencies {
  library_index: 124
  library_dep_index: 6
  library_dep_index: 125
  library_dep_index: 1
  library_dep_index: 23
  library_dep_index: 121
  library_dep_index: 127
  library_dep_index: 131
  library_dep_index: 133
  library_dep_index: 135
  library_dep_index: 136
}
library_dependencies {
  library_index: 125
  library_dep_index: 1
  library_dep_index: 126
}
library_dependencies {
  library_index: 126
  library_dep_index: 1
}
library_dependencies {
  library_index: 127
  library_dep_index: 128
}
library_dependencies {
  library_index: 128
  library_dep_index: 123
  library_dep_index: 129
  library_dep_index: 1
  library_dep_index: 23
  library_dep_index: 121
  library_dep_index: 123
  library_dep_index: 131
  library_dep_index: 133
  library_dep_index: 135
  library_dep_index: 136
}
library_dependencies {
  library_index: 129
  library_dep_index: 130
}
library_dependencies {
  library_index: 130
  library_dep_index: 0
}
library_dependencies {
  library_index: 131
  library_dep_index: 132
}
library_dependencies {
  library_index: 132
  library_dep_index: 121
  library_dep_index: 133
  library_dep_index: 1
  library_dep_index: 23
  library_dep_index: 121
  library_dep_index: 123
  library_dep_index: 127
  library_dep_index: 133
  library_dep_index: 135
  library_dep_index: 136
}
library_dependencies {
  library_index: 133
  library_dep_index: 134
}
library_dependencies {
  library_index: 134
  library_dep_index: 123
  library_dep_index: 127
  library_dep_index: 135
  library_dep_index: 129
  library_dep_index: 1
  library_dep_index: 121
  library_dep_index: 123
  library_dep_index: 127
  library_dep_index: 131
  library_dep_index: 135
  library_dep_index: 136
}
library_dependencies {
  library_index: 135
  library_dep_index: 136
  library_dep_index: 121
  library_dep_index: 123
  library_dep_index: 127
  library_dep_index: 131
  library_dep_index: 133
  library_dep_index: 136
}
library_dependencies {
  library_index: 136
  library_dep_index: 121
  library_dep_index: 123
  library_dep_index: 127
  library_dep_index: 131
  library_dep_index: 133
  library_dep_index: 135
}
library_dependencies {
  library_index: 137
  library_dep_index: 6
  library_dep_index: 4
  library_dep_index: 11
  library_dep_index: 47
  library_dep_index: 59
  library_dep_index: 72
  library_dep_index: 138
  library_dep_index: 8
}
library_dependencies {
  library_index: 138
  library_dep_index: 6
  library_dep_index: 53
  library_dep_index: 11
  library_dep_index: 81
  library_dep_index: 73
}
library_dependencies {
  library_index: 139
  library_dep_index: 6
  library_dep_index: 12
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 16
  library_dep_index: 14
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 4
  dependency_index: 61
  dependency_index: 41
  dependency_index: 75
  dependency_index: 76
  dependency_index: 77
  dependency_index: 6
  dependency_index: 78
  dependency_index: 79
  dependency_index: 84
  dependency_index: 88
  dependency_index: 89
  dependency_index: 90
  dependency_index: 91
  dependency_index: 1
  dependency_index: 11
  dependency_index: 17
  dependency_index: 96
  dependency_index: 99
  dependency_index: 100
  dependency_index: 101
  dependency_index: 105
  dependency_index: 86
  dependency_index: 5
  dependency_index: 109
  dependency_index: 114
  dependency_index: 115
  dependency_index: 121
  dependency_index: 131
  dependency_index: 137
  dependency_index: 139
  dependency_index: 140
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://storage.googleapis.com/download.flutter.io"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://jcenter.bintray.com/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
