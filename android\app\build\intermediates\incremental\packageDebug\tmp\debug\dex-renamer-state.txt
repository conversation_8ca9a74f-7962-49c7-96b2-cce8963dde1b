#Tue Jun 10 16:45:41 EEST 2025
path.4=classes1000.dex
path.3=1/classes.dex
path.2=0/classes.dex
path.1=0/classes.dex
path.6=classes3.dex
path.5=classes2.dex
path.0=classes.dex
base.4=G\:\\New folder\\android\\app\\build\\intermediates\\desugar_lib_dex\\debug\\l8DexDesugarLibDebug\\classes1000.dex
base.3=G\:\\New folder\\android\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\1\\classes.dex
base.2=G\:\\New folder\\android\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.1=G\:\\New folder\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\0\\classes.dex
base.0=G\:\\New folder\\android\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
renamed.3=classes4.dex
renamed.2=classes3.dex
renamed.1=classes2.dex
renamed.0=classes.dex
renamed.6=classes7.dex
renamed.5=classes6.dex
base.6=G\:\\New folder\\android\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes3.dex
renamed.4=classes5.dex
base.5=G\:\\New folder\\android\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes2.dex
