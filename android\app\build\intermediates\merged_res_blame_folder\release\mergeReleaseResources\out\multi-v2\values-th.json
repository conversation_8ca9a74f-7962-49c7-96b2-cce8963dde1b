{"logs": [{"outputFile": "com.moto_elmagic.app-mergeReleaseResources-76:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b93785a04fcd49acabbaa600426866db\\transformed\\jetified-play-services-base-18.3.0\\res\\values-th\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,438,557,660,792,912,1027,1131,1271,1372,1515,1633,1769,1916,1976,2040", "endColumns": "101,142,118,102,131,119,114,103,139,100,142,117,135,146,59,63,79", "endOffsets": "294,437,556,659,791,911,1026,1130,1270,1371,1514,1632,1768,1915,1975,2039,2119"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4370,4476,4623,4746,4853,4989,5113,5232,5469,5613,5718,5865,5987,6127,6278,6342,6410", "endColumns": "105,146,122,106,135,123,118,107,143,104,146,121,139,150,63,67,83", "endOffsets": "4471,4618,4741,4848,4984,5108,5227,5335,5608,5713,5860,5982,6122,6273,6337,6405,6489"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\10d17790f6d6a95ec46bd68f81a8cd4a\\transformed\\material-1.9.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,358,430,513,598,684,783,896,976,1046,1136,1206,1266,1353,1419,1484,1545,1609,1670,1724,1825,1886,1946,2000,2070,2181,2268,2349,2492,2571,2653,2745,2799,2852,2918,2988,3066,3152,3224,3302,3371,3440,3522,3610,3703,3797,3871,3940,4035,4087,4155,4240,4328,4390,4454,4517,4617,4710,4807,4900,4958,5015", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,72,71,82,84,85,98,112,79,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,80,142,78,81,91,53,52,65,69,77,85,71,77,68,68,81,87,92,93,73,68,94,51,67,84,87,61,63,62,99,92,96,92,57,56,76", "endOffsets": "280,353,425,508,593,679,778,891,971,1041,1131,1201,1261,1348,1414,1479,1540,1604,1665,1719,1820,1881,1941,1995,2065,2176,2263,2344,2487,2566,2648,2740,2794,2847,2913,2983,3061,3147,3219,3297,3366,3435,3517,3605,3698,3792,3866,3935,4030,4082,4150,4235,4323,4385,4449,4512,4612,4705,4802,4895,4953,5010,5087"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,68,70,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2964,3037,3109,3192,3277,4078,4177,4290,6667,6823,7221,7291,7351,7438,7504,7569,7630,7694,7755,7809,7910,7971,8031,8085,8155,8266,8353,8434,8577,8656,8738,8830,8884,8937,9003,9073,9151,9237,9309,9387,9456,9525,9607,9695,9788,9882,9956,10025,10120,10172,10240,10325,10413,10475,10539,10602,10702,10795,10892,10985,11043,11180", "endLines": "5,33,34,35,36,37,45,46,47,68,70,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,126", "endColumns": "12,72,71,82,84,85,98,112,79,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,80,142,78,81,91,53,52,65,69,77,85,71,77,68,68,81,87,92,93,73,68,94,51,67,84,87,61,63,62,99,92,96,92,57,56,76", "endOffsets": "330,3032,3104,3187,3272,3358,4172,4285,4365,6732,6908,7286,7346,7433,7499,7564,7625,7689,7750,7804,7905,7966,8026,8080,8150,8261,8348,8429,8572,8651,8733,8825,8879,8932,8998,9068,9146,9232,9304,9382,9451,9520,9602,9690,9783,9877,9951,10020,10115,10167,10235,10320,10408,10470,10534,10597,10697,10790,10887,10980,11038,11095,11252"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f43509670beb1f53c1f6996445ca23a\\transformed\\core-1.16.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "38,39,40,41,42,43,44,129", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3363,3459,3562,3660,3758,3861,3966,11478", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "3454,3557,3655,3753,3856,3961,4073,11574"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\df22b875ff6a90ec3ebad0ef728bb68b\\transformed\\browser-1.8.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,368", "endColumns": "102,98,110,97", "endOffsets": "153,252,363,461"}, "to": {"startLines": "67,71,72,73", "startColumns": "4,4,4,4", "startOffsets": "6564,6913,7012,7123", "endColumns": "102,98,110,97", "endOffsets": "6662,7007,7118,7216"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\48bb13e58e49cdf1e6377472107b28a8\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-th\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5340", "endColumns": "128", "endOffsets": "5464"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37c9757273d42d5a9fb8fafd595f355e\\transformed\\appcompat-1.6.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "335,440,533,641,726,828,938,1016,1093,1184,1277,1368,1462,1562,1655,1750,1844,1935,2026,2107,2210,2308,2406,2509,2615,2716,2869,11396", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "435,528,636,721,823,933,1011,1088,1179,1272,1363,1457,1557,1650,1745,1839,1930,2021,2102,2205,2303,2401,2504,2610,2711,2864,2959,11473"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\12a085f4af97d8500c9432a48145ea1b\\transformed\\preference-1.2.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,261,341,480,648,728", "endColumns": "69,85,79,138,167,79,77", "endOffsets": "170,256,336,475,643,723,801"}, "to": {"startLines": "66,69,125,127,130,131,132", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6494,6737,11100,11257,11579,11747,11827", "endColumns": "69,85,79,138,167,79,77", "endOffsets": "6559,6818,11175,11391,11742,11822,11900"}}]}]}