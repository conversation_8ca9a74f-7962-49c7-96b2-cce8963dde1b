<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2021 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<selector xmlns:android="http://schemas.android.com/apk/res/android">
  <!-- Disabled -->
  <item android:alpha="@dimen/m3_comp_outlined_text_field_disabled_input_text_opacity" android:color="@macro/m3_comp_outlined_text_field_disabled_input_text_color" android:state_enabled="false"/>
  <!-- Focused -->
  <item android:color="@macro/m3_comp_outlined_text_field_focus_input_text_color" android:state_focused="true"/>
  <!-- Hovered -->
  <item android:color="@macro/m3_comp_outlined_text_field_hover_input_text_color" android:state_hovered="true"/>
  <!-- Normal -->
  <item android:color="@macro/m3_comp_outlined_text_field_input_text_color"/>
</selector>
