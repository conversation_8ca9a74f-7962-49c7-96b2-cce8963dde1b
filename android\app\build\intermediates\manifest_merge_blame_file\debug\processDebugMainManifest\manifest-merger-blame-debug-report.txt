1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.moto_elmagic.app.debug"
4    android:versionCode="2"
5    android:versionName="1.2.0-DEBUG" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->G:\New folder\android\app\src\main\AndroidManifest.xml:5:5-67
15-->G:\New folder\android\app\src\main\AndroidManifest.xml:5:22-64
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
16-->G:\New folder\android\app\src\main\AndroidManifest.xml:6:5-79
16-->G:\New folder\android\app\src\main\AndroidManifest.xml:6:22-76
17    <uses-permission android:name="android.permission.RECORD_AUDIO" />
17-->G:\New folder\android\app\src\main\AndroidManifest.xml:7:5-70
17-->G:\New folder\android\app\src\main\AndroidManifest.xml:7:22-68
18    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
18-->G:\New folder\android\app\src\main\AndroidManifest.xml:8:5-79
18-->G:\New folder\android\app\src\main\AndroidManifest.xml:8:22-76
19    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
19-->G:\New folder\android\app\src\main\AndroidManifest.xml:9:5-81
19-->G:\New folder\android\app\src\main\AndroidManifest.xml:9:22-78
20    <uses-permission android:name="android.permission.CAMERA" />
20-->G:\New folder\android\app\src\main\AndroidManifest.xml:10:5-65
20-->G:\New folder\android\app\src\main\AndroidManifest.xml:10:22-62
21    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
21-->G:\New folder\android\app\src\main\AndroidManifest.xml:11:5-80
21-->G:\New folder\android\app\src\main\AndroidManifest.xml:11:22-77
22    <uses-permission
22-->G:\New folder\android\app\src\main\AndroidManifest.xml:12:5-146
23        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
23-->G:\New folder\android\app\src\main\AndroidManifest.xml:12:22-78
24        android:maxSdkVersion="32" />
24-->G:\New folder\android\app\src\main\AndroidManifest.xml:12:79-105
25    <uses-permission android:name="android.permission.VIBRATE" />
25-->G:\New folder\android\app\src\main\AndroidManifest.xml:13:5-66
25-->G:\New folder\android\app\src\main\AndroidManifest.xml:13:22-63
26    <!--
27 Required to query activities that can process text, see:
28         https://developer.android.com/training/package-visibility and
29         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
30
31         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
32    -->
33    <queries>
33-->G:\New folder\android\app\src\main\AndroidManifest.xml:69:5-74:15
34        <intent>
34-->G:\New folder\android\app\src\main\AndroidManifest.xml:70:9-73:18
35            <action android:name="android.intent.action.PROCESS_TEXT" />
35-->G:\New folder\android\app\src\main\AndroidManifest.xml:71:13-72
35-->G:\New folder\android\app\src\main\AndroidManifest.xml:71:21-70
36
37            <data android:mimeType="text/plain" />
37-->G:\New folder\android\app\src\main\AndroidManifest.xml:53:17-55:53
38        </intent>
39        <intent>
39-->[:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-10.1.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
40            <action android:name="android.intent.action.GET_CONTENT" />
40-->[:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-10.1.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
40-->[:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-10.1.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
41
42            <data android:mimeType="*/*" />
42-->G:\New folder\android\app\src\main\AndroidManifest.xml:53:17-55:53
43        </intent>
44    </queries>
45
46    <uses-feature android:name="android.hardware.camera.any" />
46-->[:camera_android_camerax] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\camera_android_camerax-0.6.15+2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-64
46-->[:camera_android_camerax] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\camera_android_camerax-0.6.15+2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:19-61
47
48    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
48-->[:flutter_local_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-19.2.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
48-->[:flutter_local_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-19.2.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
49
50    <permission
50-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
51        android:name="com.moto_elmagic.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
51-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
52        android:protectionLevel="signature" />
52-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
53
54    <uses-permission android:name="com.moto_elmagic.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Permission will be merged into the manifest of the hosting app. -->
54-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
54-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
55    <!-- Is required to launch foreground extraction service for targetSdkVersion 28+. -->
56    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
56-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:13:5-77
56-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:13:22-74
57
58    <application
58-->G:\New folder\android\app\src\main\AndroidManifest.xml:14:6-63:19
59        android:allowBackup="true"
59-->G:\New folder\android\app\src\main\AndroidManifest.xml:17:9-35
60        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
60-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f43509670beb1f53c1f6996445ca23a\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
61        android:debuggable="true"
62        android:extractNativeLibs="false"
62-->G:\New folder\android\app\src\main\AndroidManifest.xml:22:9-42
63        android:hardwareAccelerated="true"
63-->G:\New folder\android\app\src\main\AndroidManifest.xml:18:9-43
64        android:icon="@mipmap/ic_launcher"
64-->G:\New folder\android\app\src\main\AndroidManifest.xml:16:9-43
65        android:label="الماجيك لقطع الغيار"
65-->G:\New folder\android\app\src\main\AndroidManifest.xml:15:9-44
66        android:largeHeap="true"
66-->G:\New folder\android\app\src\main\AndroidManifest.xml:19:9-33
67        android:requestLegacyExternalStorage="true"
67-->G:\New folder\android\app\src\main\AndroidManifest.xml:21:9-52
68        android:usesCleartextTraffic="true" >
68-->G:\New folder\android\app\src\main\AndroidManifest.xml:20:9-44
69        <activity
69-->G:\New folder\android\app\src\main\AndroidManifest.xml:23:9-57:20
70            android:name="com.moto_elmagic.app.MainActivity"
70-->G:\New folder\android\app\src\main\AndroidManifest.xml:24:13-41
71            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
71-->G:\New folder\android\app\src\main\AndroidManifest.xml:29:13-163
72            android:directBootAware="true"
72-->G:\New folder\android\app\src\main\AndroidManifest.xml:35:13-43
73            android:exported="true"
73-->G:\New folder\android\app\src\main\AndroidManifest.xml:25:13-36
74            android:hardwareAccelerated="true"
74-->G:\New folder\android\app\src\main\AndroidManifest.xml:30:13-47
75            android:launchMode="singleTop"
75-->G:\New folder\android\app\src\main\AndroidManifest.xml:26:13-43
76            android:resizeableActivity="false"
76-->G:\New folder\android\app\src\main\AndroidManifest.xml:33:13-47
77            android:screenOrientation="portrait"
77-->G:\New folder\android\app\src\main\AndroidManifest.xml:32:13-49
78            android:supportsPictureInPicture="false"
78-->G:\New folder\android\app\src\main\AndroidManifest.xml:34:13-53
79            android:taskAffinity=""
79-->G:\New folder\android\app\src\main\AndroidManifest.xml:27:13-36
80            android:theme="@style/LaunchTheme"
80-->G:\New folder\android\app\src\main\AndroidManifest.xml:28:13-47
81            android:windowSoftInputMode="adjustResize" >
81-->G:\New folder\android\app\src\main\AndroidManifest.xml:31:13-55
82
83            <!--
84                 Specifies an Android theme to apply to this Activity as soon as
85                 the Android process has started. This theme is visible to the user
86                 while the Flutter UI initializes. After that, this theme continues
87                 to determine the Window background behind the Flutter UI.
88            -->
89            <meta-data
89-->G:\New folder\android\app\src\main\AndroidManifest.xml:40:13-43:17
90                android:name="io.flutter.embedding.android.NormalTheme"
90-->G:\New folder\android\app\src\main\AndroidManifest.xml:41:15-70
91                android:resource="@style/NormalTheme" />
91-->G:\New folder\android\app\src\main\AndroidManifest.xml:42:15-52
92
93            <intent-filter>
93-->G:\New folder\android\app\src\main\AndroidManifest.xml:44:13-47:29
94                <action android:name="android.intent.action.MAIN" />
94-->G:\New folder\android\app\src\main\AndroidManifest.xml:45:17-68
94-->G:\New folder\android\app\src\main\AndroidManifest.xml:45:25-66
95
96                <category android:name="android.intent.category.LAUNCHER" />
96-->G:\New folder\android\app\src\main\AndroidManifest.xml:46:17-76
96-->G:\New folder\android\app\src\main\AndroidManifest.xml:46:27-74
97            </intent-filter>
98            <!-- إضافة إعدادات إعادة التوجيه لتسجيل الدخول بجوجل -->
99            <intent-filter>
99-->G:\New folder\android\app\src\main\AndroidManifest.xml:49:13-56:29
100                <action android:name="android.intent.action.VIEW" />
100-->G:\New folder\android\app\src\main\AndroidManifest.xml:50:17-69
100-->G:\New folder\android\app\src\main\AndroidManifest.xml:50:25-66
101
102                <category android:name="android.intent.category.DEFAULT" />
102-->G:\New folder\android\app\src\main\AndroidManifest.xml:51:17-76
102-->G:\New folder\android\app\src\main\AndroidManifest.xml:51:27-73
103                <category android:name="android.intent.category.BROWSABLE" />
103-->G:\New folder\android\app\src\main\AndroidManifest.xml:52:17-78
103-->G:\New folder\android\app\src\main\AndroidManifest.xml:52:27-75
104
105                <data
105-->G:\New folder\android\app\src\main\AndroidManifest.xml:53:17-55:53
106                    android:host="login-callback"
106-->G:\New folder\android\app\src\main\AndroidManifest.xml:55:21-50
107                    android:scheme="io.supabase.motorcycleparts" />
107-->G:\New folder\android\app\src\main\AndroidManifest.xml:54:21-65
108            </intent-filter>
109        </activity>
110        <!--
111             Don't delete the meta-data below.
112             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
113        -->
114        <meta-data
114-->G:\New folder\android\app\src\main\AndroidManifest.xml:60:9-62:33
115            android:name="flutterEmbedding"
115-->G:\New folder\android\app\src\main\AndroidManifest.xml:61:13-44
116            android:value="2" />
116-->G:\New folder\android\app\src\main\AndroidManifest.xml:62:13-30
117
118        <service
118-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:24:9-33:19
119            android:name="androidx.camera.core.impl.MetadataHolderService"
119-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:25:13-75
120            android:enabled="false"
120-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:26:13-36
121            android:exported="false" >
121-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:27:13-37
122            <meta-data
122-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
123                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
123-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
124                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
124-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5075620209bdfe9a54b1b31bacd46a9f\transformed\jetified-camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
125        </service>
126        <service
126-->[:geolocator_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\geolocator_android-5.0.1+1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
127            android:name="com.baseflow.geolocator.GeolocatorLocationService"
127-->[:geolocator_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\geolocator_android-5.0.1+1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
128            android:enabled="true"
128-->[:geolocator_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\geolocator_android-5.0.1+1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
129            android:exported="false"
129-->[:geolocator_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\geolocator_android-5.0.1+1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
130            android:foregroundServiceType="location" />
130-->[:geolocator_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\geolocator_android-5.0.1+1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
131
132        <provider
132-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-19:20
133            android:name="com.crazecoder.openfile.FileProvider"
133-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
134            android:authorities="com.moto_elmagic.app.debug.fileProvider.com.crazecoder.openfile"
134-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-88
135            android:exported="false"
135-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
136            android:grantUriPermissions="true"
136-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
137            android:requestLegacyExternalStorage="true" >
137-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-56
138            <meta-data
138-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-18:53
139                android:name="android.support.FILE_PROVIDER_PATHS"
139-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:17-67
140                android:resource="@xml/filepaths" />
140-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:17-50
141        </provider>
142        <provider
142-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
143            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
143-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
144            android:authorities="com.moto_elmagic.app.debug.flutter.image_provider"
144-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
145            android:exported="false"
145-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
146            android:grantUriPermissions="true" >
146-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
147            <meta-data
147-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-18:53
148                android:name="android.support.FILE_PROVIDER_PATHS"
148-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:17-67
149                android:resource="@xml/flutter_image_picker_file_paths" />
149-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:17-50
150        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
151        <service
151-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
152            android:name="com.google.android.gms.metadata.ModuleDependencies"
152-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
153            android:enabled="false"
153-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
154            android:exported="false" >
154-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
155            <intent-filter>
155-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
156                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
156-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
156-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
157            </intent-filter>
158
159            <meta-data
159-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
160                android:name="photopicker_activity:0:required"
160-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
161                android:value="" />
161-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
162        </service>
163
164        <provider
164-->[:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
165            android:name="net.nfet.flutter.printing.PrintFileProvider"
165-->[:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-71
166            android:authorities="com.moto_elmagic.app.debug.flutter.printing"
166-->[:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-68
167            android:exported="false"
167-->[:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
168            android:grantUriPermissions="true" >
168-->[:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.14.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
169            <meta-data
169-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-18:53
170                android:name="android.support.FILE_PROVIDER_PATHS"
170-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:17-67
171                android:resource="@xml/flutter_printing_file_paths" />
171-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:17-50
172        </provider>
173        <!--
174           Declares a provider which allows us to store files to share in
175           '.../caches/share_plus' and grant the receiving action access
176        -->
177        <provider
177-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:20
178            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
178-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-77
179            android:authorities="com.moto_elmagic.app.debug.flutter.share_provider"
179-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-74
180            android:exported="false"
180-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
181            android:grantUriPermissions="true" >
181-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-47
182            <meta-data
182-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-18:53
183                android:name="android.support.FILE_PROVIDER_PATHS"
183-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:17-67
184                android:resource="@xml/flutter_share_file_paths" />
184-->[:open_file_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\open_file_android-1.0.6\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:17-50
185        </provider>
186        <!--
187           This manifest declared broadcast receiver allows us to use an explicit
188           Intent when creating a PendingItent to be informed of the user's choice
189        -->
190        <receiver
190-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-32:20
191            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
191-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-82
192            android:exported="false" >
192-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-37
193            <intent-filter>
193-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-31:29
194                <action android:name="EXTRA_CHOSEN_COMPONENT" />
194-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-65
194-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-11.0.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:25-62
195            </intent-filter>
196        </receiver>
197
198        <activity
198-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
199            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
199-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
200            android:exported="false"
200-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
201            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
201-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
202        <activity
202-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
203            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
203-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
204            android:excludeFromRecents="true"
204-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
205            android:exported="false"
205-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
206            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
206-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
207        <!--
208            Service handling Google Sign-In user revocation. For apps that do not integrate with
209            Google Sign-In, this service will never be started.
210        -->
211        <service
211-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
212            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
212-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
213            android:exported="true"
213-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
214            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
214-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
215            android:visibleToInstantApps="true" />
215-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a642d7d914df5b1457bd601ae314127\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
216
217        <activity
217-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b93785a04fcd49acabbaa600426866db\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
218            android:name="com.google.android.gms.common.api.GoogleApiActivity"
218-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b93785a04fcd49acabbaa600426866db\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
219            android:exported="false"
219-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b93785a04fcd49acabbaa600426866db\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
220            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
220-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b93785a04fcd49acabbaa600426866db\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
221
222        <meta-data
222-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48bb13e58e49cdf1e6377472107b28a8\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
223            android:name="com.google.android.gms.version"
223-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48bb13e58e49cdf1e6377472107b28a8\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
224            android:value="@integer/google_play_services_version" />
224-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48bb13e58e49cdf1e6377472107b28a8\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
225
226        <provider
226-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
227            android:name="androidx.startup.InitializationProvider"
227-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
228            android:authorities="com.moto_elmagic.app.debug.androidx-startup"
228-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
229            android:exported="false" >
229-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
230            <meta-data
230-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
231                android:name="androidx.emoji2.text.EmojiCompatInitializer"
231-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
232                android:value="androidx.startup" />
232-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2572c773145932f08e3b72fd0d530e61\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
233            <meta-data
233-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc3934e7b5992873779b6c0d978229f8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
234                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
234-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc3934e7b5992873779b6c0d978229f8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
235                android:value="androidx.startup" />
235-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc3934e7b5992873779b6c0d978229f8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
236            <meta-data
236-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
237                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
237-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
238                android:value="androidx.startup" />
238-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
239        </provider>
240
241        <uses-library
241-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
242            android:name="androidx.window.extensions"
242-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
243            android:required="false" />
243-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
244        <uses-library
244-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
245            android:name="androidx.window.sidecar"
245-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
246            android:required="false" />
246-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
247
248        <receiver
248-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
249            android:name="androidx.profileinstaller.ProfileInstallReceiver"
249-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
250            android:directBootAware="false"
250-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
251            android:enabled="true"
251-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
252            android:exported="true"
252-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
253            android:permission="android.permission.DUMP" >
253-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
254            <intent-filter>
254-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
255                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
256            </intent-filter>
257            <intent-filter>
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
258                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
259            </intent-filter>
260            <intent-filter>
260-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
261                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
261-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
261-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
262            </intent-filter>
263            <intent-filter>
263-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
264                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
264-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
264-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
265            </intent-filter>
266        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
267        <activity
267-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:18:9-24:45
268            android:name="com.google.android.play.core.missingsplits.PlayCoreMissingSplitsActivity"
268-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:19:13-100
269            android:enabled="false"
269-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:20:13-36
270            android:exported="false"
270-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:21:13-37
271            android:launchMode="singleInstance"
271-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:22:13-48
272            android:process=":playcore_missing_splits_activity"
272-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:23:13-64
273            android:stateNotNeeded="true" />
273-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:24:13-42
274        <activity
274-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:25:9-29:65
275            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
275-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:26:13-93
276            android:exported="false"
276-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:27:13-37
277            android:stateNotNeeded="true"
277-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:28:13-42
278            android:theme="@style/Theme.PlayCore.Transparent" /> <!-- The services will be merged into the manifest of the hosting app. -->
278-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:29:13-62
279        <service
279-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:32:9-39:19
280            android:name="com.google.android.play.core.assetpacks.AssetPackExtractionService"
280-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:33:13-94
281            android:enabled="false"
281-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:34:13-36
282            android:exported="true" >
282-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:35:13-36
283            <meta-data
283-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:36:13-38:41
284                android:name="com.google.android.play.core.assetpacks.versionCode"
284-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:37:17-83
285                android:value="11003" />
285-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:38:17-38
286        </service>
287        <service
287-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:40:9-43:40
288            android:name="com.google.android.play.core.assetpacks.ExtractionForegroundService"
288-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:41:13-95
289            android:enabled="false"
289-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:42:13-36
290            android:exported="false" />
290-->[com.google.android.play:core:1.10.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3da4333aa935e250dec89c6f0e2864b7\transformed\jetified-core-1.10.3\AndroidManifest.xml:43:13-37
291    </application>
292
293</manifest>
