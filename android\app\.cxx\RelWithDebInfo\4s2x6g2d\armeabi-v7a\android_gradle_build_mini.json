{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "G:\\New folder\\android\\app\\.cxx\\RelWithDebInfo\\4s2x6g2d\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "G:\\New folder\\android\\app\\.cxx\\RelWithDebInfo\\4s2x6g2d\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}