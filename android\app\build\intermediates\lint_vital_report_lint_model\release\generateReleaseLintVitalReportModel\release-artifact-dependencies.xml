<dependencies>
  <compile
      roots="__local_aars__:G:\New folder\android\app\build\intermediates\flutter\release\libs.jar:unspecified@jar,:@@:camera_android_camerax::release,:@@:file_picker::release,:@@:shared_preferences_android::release,:@@:speech_to_text::release,com.google.android.material:material:1.9.0@aar,androidx.constraintlayout:constraintlayout:2.0.1@aar,androidx.appcompat:appcompat:1.6.1@aar,androidx.viewpager2:viewpager2:1.0.0@aar,:@@:app_links::release,:@@:connectivity_plus::release,:@@:device_info_plus::release,:@@:flutter_local_notifications::release,:@@:flutter_plugin_android_lifecycle::release,:@@:geolocator_android::release,:@@:google_sign_in_android::release,:@@:image_picker_android::release,:@@:integration_test::release,:@@:open_file_android::release,:@@:package_info_plus::release,:@@:path_provider_android::release,:@@:printing::release,:@@:share_plus::release,:@@:sqflite_android::release,:@@:url_launcher_android::release,:@@:vibration::release,io.flutter:flutter_embedding_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar,androidx.fragment:fragment:1.7.1@aar,androidx.fragment:fragment:1.7.1@aar,androidx.activity:activity:1.9.3@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.1.0@aar,androidx.appcompat:appcompat-resources:1.6.1@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.recyclerview:recyclerview:1.1.0@aar,androidx.transition:transition:1.4.1@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.core:core:1.16.0@aar,androidx.core:core:1.16.0@aar,androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar,androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar,androidx.lifecycle:lifecycle-process:2.8.7@aar,androidx.lifecycle:lifecycle-common-java8:2.8.7@jar,androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar,androidx.core:core-ktx:1.16.0@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar,androidx.savedstate:savedstate:1.2.1@aar,androidx.annotation:annotation-experimental:1.4.1@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.cardview:cardview:1.0.0@aar,androidx.test:rules:1.2.0@aar,androidx.test.espresso:espresso-core:3.2.0@aar,androidx.test:runner:1.2.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.test:monitor:1.2.0@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.collection:collection-jvm:1.4.2@jar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.annotation:annotation-jvm:1.9.1@jar,androidx.core:core-viewtree:1.0.0@aar,androidx.window:window:1.2.0@aar,androidx.window:window-java:1.2.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar,io.flutter:armeabi_v7a_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar,io.flutter:arm64_v8a_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar,io.flutter:x86_64_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar,org.jetbrains:annotations:23.0.0@jar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,org.jspecify:jspecify:1.0.0@jar,androidx.constraintlayout:constraintlayout-solver:2.0.1@jar,com.getkeepsafe.relinker:relinker:1.4.5@aar,com.google.code.findbugs:jsr305:3.0.2@jar,junit:junit:4.12@jar,org.hamcrest:hamcrest-integration:1.3@jar,org.hamcrest:hamcrest-library:1.3@jar,org.hamcrest:hamcrest-core:1.3@jar,net.sf.kxml:kxml2:2.3.0@jar,androidx.test.espresso:espresso-idling-resource:3.2.0@aar,com.squareup:javawriter:2.1.1@jar,javax.inject:javax.inject:1@jar">
    <dependency
        name="__local_aars__:G:\New folder\android\app\build\intermediates\flutter\release\libs.jar:unspecified@jar"
        simpleName="__local_aars__:G:\New folder\android\app\build\intermediates\flutter\release\libs.jar"/>
    <dependency
        name=":@@:camera_android_camerax::release"
        simpleName="artifacts::camera_android_camerax"/>
    <dependency
        name=":@@:file_picker::release"
        simpleName="artifacts::file_picker"/>
    <dependency
        name=":@@:shared_preferences_android::release"
        simpleName="artifacts::shared_preferences_android"/>
    <dependency
        name=":@@:speech_to_text::release"
        simpleName="artifacts::speech_to_text"/>
    <dependency
        name="com.google.android.material:material:1.9.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.0.1@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="androidx.appcompat:appcompat:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.0.0@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name=":@@:app_links::release"
        simpleName="artifacts::app_links"/>
    <dependency
        name=":@@:connectivity_plus::release"
        simpleName="artifacts::connectivity_plus"/>
    <dependency
        name=":@@:device_info_plus::release"
        simpleName="artifacts::device_info_plus"/>
    <dependency
        name=":@@:flutter_local_notifications::release"
        simpleName="artifacts::flutter_local_notifications"/>
    <dependency
        name=":@@:flutter_plugin_android_lifecycle::release"
        simpleName="artifacts::flutter_plugin_android_lifecycle"/>
    <dependency
        name=":@@:geolocator_android::release"
        simpleName="artifacts::geolocator_android"/>
    <dependency
        name=":@@:google_sign_in_android::release"
        simpleName="artifacts::google_sign_in_android"/>
    <dependency
        name=":@@:image_picker_android::release"
        simpleName="artifacts::image_picker_android"/>
    <dependency
        name=":@@:integration_test::release"
        simpleName="artifacts::integration_test"/>
    <dependency
        name=":@@:open_file_android::release"
        simpleName="artifacts::open_file_android"/>
    <dependency
        name=":@@:package_info_plus::release"
        simpleName="artifacts::package_info_plus"/>
    <dependency
        name=":@@:path_provider_android::release"
        simpleName="artifacts::path_provider_android"/>
    <dependency
        name=":@@:printing::release"
        simpleName="artifacts::printing"/>
    <dependency
        name=":@@:share_plus::release"
        simpleName="artifacts::share_plus"/>
    <dependency
        name=":@@:sqflite_android::release"
        simpleName="artifacts::sqflite_android"/>
    <dependency
        name=":@@:url_launcher_android::release"
        simpleName="artifacts::url_launcher_android"/>
    <dependency
        name=":@@:vibration::release"
        simpleName="artifacts::vibration"/>
    <dependency
        name="io.flutter:flutter_embedding_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar"
        simpleName="io.flutter:flutter_embedding_release"/>
    <dependency
        name="androidx.fragment:fragment:1.7.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.9.3@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.1.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.1.0@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.transition:transition:1.4.1@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.16.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.8.7@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.16.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.test:rules:1.2.0@aar"
        simpleName="androidx.test:rules"/>
    <dependency
        name="androidx.test.espresso:espresso-core:3.2.0@aar"
        simpleName="androidx.test.espresso:espresso-core"/>
    <dependency
        name="androidx.test:runner:1.2.0@aar"
        simpleName="androidx.test:runner"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.test:monitor:1.2.0@aar"
        simpleName="androidx.test:monitor"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.2@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.core:core-viewtree:1.0.0@aar"
        simpleName="androidx.core:core-viewtree"/>
    <dependency
        name="androidx.window:window:1.2.0@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="androidx.window:window-java:1.2.0@aar"
        simpleName="androidx.window:window-java"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="io.flutter:armeabi_v7a_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar"
        simpleName="io.flutter:armeabi_v7a_release"/>
    <dependency
        name="io.flutter:arm64_v8a_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar"
        simpleName="io.flutter:arm64_v8a_release"/>
    <dependency
        name="io.flutter:x86_64_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar"
        simpleName="io.flutter:x86_64_release"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="org.jspecify:jspecify:1.0.0@jar"
        simpleName="org.jspecify:jspecify"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout-solver:2.0.1@jar"
        simpleName="androidx.constraintlayout:constraintlayout-solver"/>
    <dependency
        name="com.getkeepsafe.relinker:relinker:1.4.5@aar"
        simpleName="com.getkeepsafe.relinker:relinker"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="junit:junit:4.12@jar"
        simpleName="junit:junit"/>
    <dependency
        name="org.hamcrest:hamcrest-integration:1.3@jar"
        simpleName="org.hamcrest:hamcrest-integration"/>
    <dependency
        name="org.hamcrest:hamcrest-library:1.3@jar"
        simpleName="org.hamcrest:hamcrest-library"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
    <dependency
        name="net.sf.kxml:kxml2:2.3.0@jar"
        simpleName="net.sf.kxml:kxml2"/>
    <dependency
        name="androidx.test.espresso:espresso-idling-resource:3.2.0@aar"
        simpleName="androidx.test.espresso:espresso-idling-resource"/>
    <dependency
        name="com.squareup:javawriter:2.1.1@jar"
        simpleName="com.squareup:javawriter"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
  </compile>
  <package
      roots="__local_aars__:G:\New folder\android\app\build\intermediates\flutter\release\libs.jar:unspecified@jar,:@@:camera_android_camerax::release,:@@:file_picker::release,:@@:shared_preferences_android::release,:@@:speech_to_text::release,androidx.camera:camera-video:1.4.1@aar,androidx.camera:camera-lifecycle:1.4.1@aar,androidx.camera:camera-camera2:1.4.1@aar,androidx.camera:camera-core:1.4.1@aar,:@@:geolocator_android::release,com.google.android.gms:play-services-location:21.2.0@aar,com.google.android.material:material:1.9.0@aar,:@@:open_file_android::release,androidx.appcompat:appcompat-resources:1.6.1@aar,androidx.constraintlayout:constraintlayout:2.0.1@aar,androidx.preference:preference:1.2.1@aar,androidx.appcompat:appcompat:1.6.1@aar,:@@:app_links::release,:@@:connectivity_plus::release,:@@:device_info_plus::release,:@@:flutter_local_notifications::release,:@@:image_picker_android::release,:@@:flutter_plugin_android_lifecycle::release,:@@:google_sign_in_android::release,:@@:integration_test::release,:@@:package_info_plus::release,:@@:path_provider_android::release,:@@:printing::release,:@@:share_plus::release,:@@:sqflite_android::release,:@@:url_launcher_android::release,:@@:vibration::release,io.flutter:flutter_embedding_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar,androidx.emoji2:emoji2-views-helper:1.2.0@aar,androidx.emoji2:emoji2:1.2.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.viewpager2:viewpager2:1.0.0@aar,androidx.media:media:1.1.0@aar,com.google.android.gms:play-services-auth:21.0.0@aar,com.google.android.gms:play-services-auth-api-phone:18.0.2@aar,com.google.android.gms:play-services-auth-base:18.0.10@aar,com.google.android.gms:play-services-fido:20.0.1@aar,com.google.android.gms:play-services-base:18.3.0@aar,com.google.android.gms:play-services-tasks:18.1.0@aar,com.google.android.gms:play-services-basement:18.3.0@aar,androidx.fragment:fragment-ktx:1.7.1@aar,androidx.fragment:fragment:1.7.1@aar,androidx.fragment:fragment:1.7.1@aar,androidx.activity:activity-ktx:1.9.3@aar,androidx.activity:activity:1.9.3@aar,androidx.browser:browser:1.8.0@aar,androidx.recyclerview:recyclerview:1.1.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.window:window:1.2.0@aar,androidx.window:window-java:1.2.0@aar,androidx.loader:loader:1.1.0@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar,androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7@aar,androidx.lifecycle:lifecycle-process:2.8.7@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar,androidx.lifecycle:lifecycle-livedata:2.8.7@aar,androidx.lifecycle:lifecycle-common-java8:2.8.7@jar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar,androidx.core:core-ktx:1.16.0@aar,androidx.transition:transition:1.4.1@aar,androidx.core:core:1.16.0@aar,androidx.core:core:1.16.0@aar,androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar,androidx.concurrent:concurrent-futures-ktx:1.1.0@jar,androidx.datastore:datastore-preferences-external-protobuf:1.1.3@jar,androidx.datastore:datastore-preferences-proto:1.1.3@jar,androidx.datastore:datastore-preferences-core-jvm:1.1.3@jar,androidx.datastore:datastore-core-okio-jvm:1.1.3@jar,androidx.datastore:datastore-core-android:1.1.3@aar,androidx.datastore:datastore-preferences-android:1.1.3@aar,androidx.datastore:datastore-android:1.1.3@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar,androidx.annotation:annotation-experimental:1.4.1@aar,androidx.profileinstaller:profileinstaller:1.3.1@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,androidx.tracing:tracing-ktx:1.2.0@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.cardview:cardview:1.0.0@aar,androidx.exifinterface:exifinterface:1.3.7@aar,androidx.test:rules:1.2.0@aar,androidx.test.espresso:espresso-core:3.2.0@aar,androidx.test:runner:1.2.0@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.interpolator:interpolator:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.test:monitor:1.2.0@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.collection:collection-ktx:1.4.2@jar,androidx.collection:collection-jvm:1.4.2@jar,androidx.window.extensions.core:core:1.0.0@aar,androidx.annotation:annotation-jvm:1.9.1@jar,androidx.core:core-viewtree:1.0.0@aar,org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar,com.squareup.okio:okio-jvm:3.4.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar,io.flutter:armeabi_v7a_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar,io.flutter:arm64_v8a_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar,io.flutter:x86_64_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar,com.google.guava:guava:33.4.0-android@jar,com.google.code.gson:gson:2.12.0@jar,com.google.errorprone:error_prone_annotations:2.36.0@jar,org.microg:safe-parcel:1.7.0@aar,org.apache.tika:tika-core:3.1.0@jar,org.jetbrains:annotations:23.0.0@jar,androidx.constraintlayout:constraintlayout-solver:2.0.1@jar,com.getkeepsafe.relinker:relinker:1.4.5@aar,com.google.auto.value:auto-value-annotations:1.6.3@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,com.google.guava:failureaccess:1.0.2@jar,com.google.code.findbugs:jsr305:3.0.2@jar,org.checkerframework:checker-qual:3.43.0@jar,com.google.j2objc:j2objc-annotations:3.0.0@jar,org.slf4j:slf4j-api:2.0.16@jar,commons-io:commons-io:2.18.0@jar,org.jspecify:jspecify:1.0.0@jar,junit:junit:4.12@jar,net.sf.kxml:kxml2:2.3.0@jar,androidx.test.espresso:espresso-idling-resource:3.2.0@aar,com.squareup:javawriter:2.1.1@jar,javax.inject:javax.inject:1@jar,org.hamcrest:hamcrest-integration:1.3@jar,org.hamcrest:hamcrest-library:1.3@jar,org.hamcrest:hamcrest-core:1.3@jar">
    <dependency
        name="__local_aars__:G:\New folder\android\app\build\intermediates\flutter\release\libs.jar:unspecified@jar"
        simpleName="__local_aars__:G:\New folder\android\app\build\intermediates\flutter\release\libs.jar"/>
    <dependency
        name=":@@:camera_android_camerax::release"
        simpleName="artifacts::camera_android_camerax"/>
    <dependency
        name=":@@:file_picker::release"
        simpleName="artifacts::file_picker"/>
    <dependency
        name=":@@:shared_preferences_android::release"
        simpleName="artifacts::shared_preferences_android"/>
    <dependency
        name=":@@:speech_to_text::release"
        simpleName="artifacts::speech_to_text"/>
    <dependency
        name="androidx.camera:camera-video:1.4.1@aar"
        simpleName="androidx.camera:camera-video"/>
    <dependency
        name="androidx.camera:camera-lifecycle:1.4.1@aar"
        simpleName="androidx.camera:camera-lifecycle"/>
    <dependency
        name="androidx.camera:camera-camera2:1.4.1@aar"
        simpleName="androidx.camera:camera-camera2"/>
    <dependency
        name="androidx.camera:camera-core:1.4.1@aar"
        simpleName="androidx.camera:camera-core"/>
    <dependency
        name=":@@:geolocator_android::release"
        simpleName="artifacts::geolocator_android"/>
    <dependency
        name="com.google.android.gms:play-services-location:21.2.0@aar"
        simpleName="com.google.android.gms:play-services-location"/>
    <dependency
        name="com.google.android.material:material:1.9.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name=":@@:open_file_android::release"
        simpleName="artifacts::open_file_android"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.0.1@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="androidx.preference:preference:1.2.1@aar"
        simpleName="androidx.preference:preference"/>
    <dependency
        name="androidx.appcompat:appcompat:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name=":@@:app_links::release"
        simpleName="artifacts::app_links"/>
    <dependency
        name=":@@:connectivity_plus::release"
        simpleName="artifacts::connectivity_plus"/>
    <dependency
        name=":@@:device_info_plus::release"
        simpleName="artifacts::device_info_plus"/>
    <dependency
        name=":@@:flutter_local_notifications::release"
        simpleName="artifacts::flutter_local_notifications"/>
    <dependency
        name=":@@:image_picker_android::release"
        simpleName="artifacts::image_picker_android"/>
    <dependency
        name=":@@:flutter_plugin_android_lifecycle::release"
        simpleName="artifacts::flutter_plugin_android_lifecycle"/>
    <dependency
        name=":@@:google_sign_in_android::release"
        simpleName="artifacts::google_sign_in_android"/>
    <dependency
        name=":@@:integration_test::release"
        simpleName="artifacts::integration_test"/>
    <dependency
        name=":@@:package_info_plus::release"
        simpleName="artifacts::package_info_plus"/>
    <dependency
        name=":@@:path_provider_android::release"
        simpleName="artifacts::path_provider_android"/>
    <dependency
        name=":@@:printing::release"
        simpleName="artifacts::printing"/>
    <dependency
        name=":@@:share_plus::release"
        simpleName="artifacts::share_plus"/>
    <dependency
        name=":@@:sqflite_android::release"
        simpleName="artifacts::sqflite_android"/>
    <dependency
        name=":@@:url_launcher_android::release"
        simpleName="artifacts::url_launcher_android"/>
    <dependency
        name=":@@:vibration::release"
        simpleName="artifacts::vibration"/>
    <dependency
        name="io.flutter:flutter_embedding_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar"
        simpleName="io.flutter:flutter_embedding_release"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.2.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2:1.2.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.0.0@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="androidx.media:media:1.1.0@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="com.google.android.gms:play-services-auth:21.0.0@aar"
        simpleName="com.google.android.gms:play-services-auth"/>
    <dependency
        name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
        simpleName="com.google.android.gms:play-services-auth-api-phone"/>
    <dependency
        name="com.google.android.gms:play-services-auth-base:18.0.10@aar"
        simpleName="com.google.android.gms:play-services-auth-base"/>
    <dependency
        name="com.google.android.gms:play-services-fido:20.0.1@aar"
        simpleName="com.google.android.gms:play-services-fido"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.3.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.1.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.3.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.7.1@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="androidx.fragment:fragment:1.7.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity-ktx:1.9.3@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity:1.9.3@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.browser:browser:1.8.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.1.0@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.window:window:1.2.0@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="androidx.window:window-java:1.2.0@aar"
        simpleName="androidx.window:window-java"/>
    <dependency
        name="androidx.loader:loader:1.1.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.8.7@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.16.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.transition:transition:1.4.1@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.core:core:1.16.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.concurrent:concurrent-futures-ktx:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures-ktx"/>
    <dependency
        name="androidx.datastore:datastore-preferences-external-protobuf:1.1.3@jar"
        simpleName="androidx.datastore:datastore-preferences-external-protobuf"/>
    <dependency
        name="androidx.datastore:datastore-preferences-proto:1.1.3@jar"
        simpleName="androidx.datastore:datastore-preferences-proto"/>
    <dependency
        name="androidx.datastore:datastore-preferences-core-jvm:1.1.3@jar"
        simpleName="androidx.datastore:datastore-preferences-core-jvm"/>
    <dependency
        name="androidx.datastore:datastore-core-okio-jvm:1.1.3@jar"
        simpleName="androidx.datastore:datastore-core-okio-jvm"/>
    <dependency
        name="androidx.datastore:datastore-core-android:1.1.3@aar"
        simpleName="androidx.datastore:datastore-core-android"/>
    <dependency
        name="androidx.datastore:datastore-preferences-android:1.1.3@aar"
        simpleName="androidx.datastore:datastore-preferences-android"/>
    <dependency
        name="androidx.datastore:datastore-android:1.1.3@aar"
        simpleName="androidx.datastore:datastore-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.tracing:tracing-ktx:1.2.0@aar"
        simpleName="androidx.tracing:tracing-ktx"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.7@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="androidx.test:rules:1.2.0@aar"
        simpleName="androidx.test:rules"/>
    <dependency
        name="androidx.test.espresso:espresso-core:3.2.0@aar"
        simpleName="androidx.test.espresso:espresso-core"/>
    <dependency
        name="androidx.test:runner:1.2.0@aar"
        simpleName="androidx.test:runner"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.test:monitor:1.2.0@aar"
        simpleName="androidx.test:monitor"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.collection:collection-ktx:1.4.2@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.2@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.window.extensions.core:core:1.0.0@aar"
        simpleName="androidx.window.extensions.core:core"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.core:core-viewtree:1.0.0@aar"
        simpleName="androidx.core:core-viewtree"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.4.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="io.flutter:armeabi_v7a_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar"
        simpleName="io.flutter:armeabi_v7a_release"/>
    <dependency
        name="io.flutter:arm64_v8a_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar"
        simpleName="io.flutter:arm64_v8a_release"/>
    <dependency
        name="io.flutter:x86_64_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar"
        simpleName="io.flutter:x86_64_release"/>
    <dependency
        name="com.google.guava:guava:33.4.0-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.google.code.gson:gson:2.12.0@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.36.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="org.microg:safe-parcel:1.7.0@aar"
        simpleName="org.microg:safe-parcel"/>
    <dependency
        name="org.apache.tika:tika-core:3.1.0@jar"
        simpleName="org.apache.tika:tika-core"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout-solver:2.0.1@jar"
        simpleName="androidx.constraintlayout:constraintlayout-solver"/>
    <dependency
        name="com.getkeepsafe.relinker:relinker:1.4.5@aar"
        simpleName="com.getkeepsafe.relinker:relinker"/>
    <dependency
        name="com.google.auto.value:auto-value-annotations:1.6.3@jar"
        simpleName="com.google.auto.value:auto-value-annotations"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.2@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.checkerframework:checker-qual:3.43.0@jar"
        simpleName="org.checkerframework:checker-qual"/>
    <dependency
        name="com.google.j2objc:j2objc-annotations:3.0.0@jar"
        simpleName="com.google.j2objc:j2objc-annotations"/>
    <dependency
        name="org.slf4j:slf4j-api:2.0.16@jar"
        simpleName="org.slf4j:slf4j-api"/>
    <dependency
        name="commons-io:commons-io:2.18.0@jar"
        simpleName="commons-io:commons-io"/>
    <dependency
        name="org.jspecify:jspecify:1.0.0@jar"
        simpleName="org.jspecify:jspecify"/>
    <dependency
        name="junit:junit:4.12@jar"
        simpleName="junit:junit"/>
    <dependency
        name="net.sf.kxml:kxml2:2.3.0@jar"
        simpleName="net.sf.kxml:kxml2"/>
    <dependency
        name="androidx.test.espresso:espresso-idling-resource:3.2.0@aar"
        simpleName="androidx.test.espresso:espresso-idling-resource"/>
    <dependency
        name="com.squareup:javawriter:2.1.1@jar"
        simpleName="com.squareup:javawriter"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="org.hamcrest:hamcrest-integration:1.3@jar"
        simpleName="org.hamcrest:hamcrest-integration"/>
    <dependency
        name="org.hamcrest:hamcrest-library:1.3@jar"
        simpleName="org.hamcrest:hamcrest-library"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
  </package>
</dependencies>
